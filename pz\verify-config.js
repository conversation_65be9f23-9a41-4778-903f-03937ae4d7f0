// 验证新食物识别功能配置
console.log('🔍 [验证] 开始验证新食物识别功能配置...');

try {
  // 检查通义千问VL-MAX配置
  console.log('🔍 [验证] 检查通义千问VL-MAX配置...');
  const configModule = await import('./utils/ai/config.js');
  const { QWEN_VL_CONFIG, DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL } = configModule;
  
  console.log('✅ 通义千问VL-MAX配置:');
  console.log('  - API Key:', QWEN_VL_CONFIG.API_KEY ? '已配置 ✓' : '未配置 ✗');
  console.log('  - Base URL:', QWEN_VL_CONFIG.BASE_URL);
  console.log('  - Model:', QWEN_VL_CONFIG.MODEL);
  console.log('  - Timeout:', QWEN_VL_CONFIG.TIMEOUT);
  
  console.log('✅ DeepSeek配置:');
  console.log('  - API Key:', DEEPSEEK_API_KEY ? '已配置 ✓' : '未配置 ✗');
  console.log('  - Base URL:', DEEPSEEK_BASE_URL);
  
  // 检查服务文件
  console.log('🔍 [验证] 检查服务文件...');
  const qwenService = await import('./utils/ai/qwenVLService.js');
  console.log('✅ qwenVLService.js 导入成功');
  
  const aiService = await import('./utils/ai/service.js');
  console.log('✅ service.js 导入成功');
  
  console.log('🎉 [验证] 所有配置验证完成！');
  console.log('📋 [验证] 新食物识别功能已准备就绪');
  
} catch (error) {
  console.error('❌ [验证] 配置验证失败:', error.message);
  console.error('🔧 [验证] 请检查配置文件和导入路径');
}

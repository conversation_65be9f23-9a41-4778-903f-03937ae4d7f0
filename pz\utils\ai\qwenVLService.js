// 通义千问VL-MAX图像识别服务
import { QWEN_VL_CONFIG } from './config.js';

/**
 * 将图片转换为base64格式
 * @param {string} filePath 图片文件路径
 * @returns {Promise<string>} base64编码的图片数据
 */
function getBase64(filePath) {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'base64',
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        console.error('读取图片文件失败:', err);
        reject(new Error('读取图片文件失败'));
      }
    });
  });
}

/**
 * 调用通义千问VL-MAX进行食物图像识别
 * @param {string} imagePath 图片路径
 * @returns {Promise<string>} 图像描述结果
 */
export async function analyzeImageWithQwenVL(imagePath) {
  try {
    console.log('🔍 [通义千问VL-MAX] 开始分析食物图片...');
    console.log('🔍 [通义千问VL-MAX] 图片路径:', imagePath);

    // 获取图片的base64编码
    const base64Image = await getBase64(imagePath);
    console.log('🔍 [通义千问VL-MAX] 图片base64编码获取成功，长度:', base64Image.length);

    // 构建请求数据
    const requestData = {
      model: QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: `请详细分析这张食物图片，包括以下信息：
1. 食物种类和名称（尽可能具体）
2. 食物的数量和大小
3. 食物的外观特征（颜色、形状、质地等）
4. 烹饪方式（如炒、煮、蒸、烤等）
5. 配菜和搭配情况
6. 食物的新鲜程度和品质
7. 估计的份量大小
8. 可能的营养特点

请用中文详细描述，描述越详细越好，这将用于后续的营养分析。`
              },
              {
                image: `data:image/jpeg;base64,${base64Image}`
              }
            ]
          }
        ]
      },
      parameters: {
        result_format: "message"
      }
    };

    console.log('🔍 [通义千问VL-MAX] 发送请求到API...');

    // 发送请求到通义千问VL-MAX API
    const response = await uni.request({
      url: QWEN_VL_CONFIG.BASE_URL,
      method: 'POST',
      data: requestData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: QWEN_VL_CONFIG.TIMEOUT
    });

    console.log('🔍 [通义千问VL-MAX] API响应状态:', response.statusCode);
    console.log('🔍 [通义千问VL-MAX] API响应数据完整结构:', JSON.stringify(response.data, null, 2));

    // 检查响应状态
    if (response.statusCode !== 200) {
      throw new Error(`通义千问VL-MAX API请求失败: HTTP ${response.statusCode}`);
    }

    // 解析响应数据
    const responseData = response.data;
    console.log('🔍 [通义千问VL-MAX] responseData:', responseData);
    console.log('🔍 [通义千问VL-MAX] responseData.output:', responseData?.output);

    if (!responseData || !responseData.output) {
      throw new Error(`通义千问VL-MAX API返回错误: ${responseData?.message || '未知错误'}`);
    }

    // 详细检查数据结构
    console.log('🔍 [通义千问VL-MAX] output.choices:', responseData.output?.choices);
    console.log('🔍 [通义千问VL-MAX] choices[0]:', responseData.output?.choices?.[0]);
    console.log('🔍 [通义千问VL-MAX] choices[0].message:', responseData.output?.choices?.[0]?.message);
    console.log('🔍 [通义千问VL-MAX] choices[0].message.content:', responseData.output?.choices?.[0]?.message?.content);

    // 提取图像描述结果
    const imageDescription = responseData.output?.choices?.[0]?.message?.content;
    console.log('🔍 [通义千问VL-MAX] 提取的imageDescription:', imageDescription);
    console.log('🔍 [通义千问VL-MAX] imageDescription类型:', typeof imageDescription);

    if (!imageDescription) {
      // 尝试其他可能的路径
      console.log('🔍 [通义千问VL-MAX] 尝试其他数据路径...');
      console.log('🔍 [通义千问VL-MAX] responseData.output.text:', responseData.output?.text);
      console.log('🔍 [通义千问VL-MAX] responseData.output.content:', responseData.output?.content);
      console.log('🔍 [通义千问VL-MAX] responseData.choices:', responseData?.choices);

      throw new Error('通义千问VL-MAX API返回的数据格式不正确');
    }

    console.log('🔍 [通义千问VL-MAX] 图像识别成功');
    console.log('🔍 [通义千问VL-MAX] 识别结果类型:', typeof imageDescription);
    console.log('🔍 [通义千问VL-MAX] 识别结果:', imageDescription);

    // 处理不同类型的返回结果
    let finalResult = '';

    if (typeof imageDescription === 'string') {
      finalResult = imageDescription;
    } else if (imageDescription && typeof imageDescription === 'object') {
      console.log('🔍 [通义千问VL-MAX] imageDescription是对象，尝试提取文本...');

      // 尝试多种可能的字段
      finalResult = imageDescription.text ||
        imageDescription.content ||
        imageDescription.message ||
        imageDescription.value ||
        imageDescription.result;

      // 如果还是对象，继续深入提取
      if (typeof finalResult === 'object' && finalResult) {
        finalResult = finalResult.text ||
          finalResult.content ||
          finalResult.message ||
          JSON.stringify(finalResult);
      }

      // 如果仍然没有找到文本，序列化整个对象
      if (!finalResult || typeof finalResult !== 'string') {
        console.log('🔍 [通义千问VL-MAX] 无法提取文本，使用JSON序列化');
        finalResult = JSON.stringify(imageDescription);
      }
    } else {
      finalResult = String(imageDescription || '');
    }

    console.log('🔍 [通义千问VL-MAX] 最终处理结果:', finalResult);
    console.log('🔍 [通义千问VL-MAX] 最终结果类型:', typeof finalResult);
    console.log('🔍 [通义千问VL-MAX] 最终结果长度:', finalResult.length);

    // 清理和简化描述，生成用户友好的版本
    const userFriendlyDescription = cleanAndSimplifyDescription(finalResult);
    console.log('🔍 [通义千问VL-MAX] 用户友好描述:', userFriendlyDescription);

    // 返回包含原始结果和用户友好描述的对象
    return {
      rawDescription: finalResult,           // 原始详细描述（用于DeepSeek分析）
      userDescription: userFriendlyDescription  // 用户友好描述（用于界面显示）
    };

  } catch (error) {
    console.error('🔍 [通义千问VL-MAX] 图像识别失败:', error);

    // 根据错误类型提供不同的错误信息
    if (error.message.includes('网络')) {
      throw new Error('网络连接失败，请检查网络设置后重试');
    } else if (error.message.includes('API')) {
      throw new Error('图像识别服务暂时不可用，请稍后重试');
    } else {
      throw new Error(`图像识别失败: ${error.message}`);
    }
  }
}

/**
 * 清理和简化AI识别结果，转换为用户友好的描述
 * @param {string} rawDescription 原始AI识别结果
 * @returns {string} 清理后的用户友好描述
 */
export function cleanAndSimplifyDescription(rawDescription) {
  if (!rawDescription || typeof rawDescription !== 'string') {
    return '未能识别到具体食物信息';
  }

  try {
    // 移除所有格式化字符和技术标记
    let cleaned = rawDescription
      .replace(/\\n/g, ' ')           // 移除换行符
      .replace(/\\t/g, ' ')           // 移除制表符
      .replace(/\n/g, ' ')            // 移除实际换行
      .replace(/\t/g, ' ')            // 移除实际制表符
      .replace(/###\s*\d+\./g, '')    // 移除编号标记如 "### 1."
      .replace(/\*\*/g, '')           // 移除加粗标记
      .replace(/\*/g, '')             // 移除星号
      .replace(/#{1,6}\s*/g, '')      // 移除标题标记
      .replace(/\[.*?\]/g, '')        // 移除方括号内容
      .replace(/\{.*?\}/g, '')        // 移除花括号内容
      .replace(/请详细分析.*?营养分析。/g, '') // 移除提示词
      .replace(/包括以下信息.*?营养特点/g, '') // 移除指令部分
      .replace(/\d+\.\s*/g, '')       // 移除数字编号
      .replace(/：\s*/g, '：')        // 规范冒号格式
      .replace(/\s+/g, ' ')           // 合并多个空格
      .trim();

    // 提取核心食物信息
    const foodPatterns = [
      /(?:图片中显示|可以看到|识别到).*?([^。]+)/,
      /主要.*?([^。]+)/,
      /食物.*?([^。]+)/,
      /([^。]*(?:米饭|面条|蔬菜|肉类|鱼类|水果|汤|粥|面包|蛋糕|饼干)[^。]*)/
    ];

    let extractedInfo = [];

    // 尝试提取主要食物信息
    for (const pattern of foodPatterns) {
      const match = cleaned.match(pattern);
      if (match && match[1]) {
        const info = match[1].trim();
        if (info.length > 5 && info.length < 100) {
          extractedInfo.push(info);
        }
      }
    }

    // 如果没有提取到有效信息，尝试获取前100个字符
    if (extractedInfo.length === 0) {
      const firstSentence = cleaned.split(/[。！？]/)[0];
      if (firstSentence && firstSentence.length > 10) {
        extractedInfo.push(firstSentence);
      }
    }

    // 生成最终的用户友好描述
    if (extractedInfo.length > 0) {
      let result = extractedInfo[0];

      // 确保描述以合适的方式开始
      if (!result.match(/^(识别到|图片中|可以看到)/)) {
        result = '识别到' + result;
      }

      // 确保描述以句号结尾
      if (!result.endsWith('。') && !result.endsWith('！') && !result.endsWith('？')) {
        result += '。';
      }

      // 限制长度，保持简洁
      if (result.length > 80) {
        result = result.substring(0, 77) + '...';
      }

      return result;
    }

    return '识别到食物图片，正在进行营养分析。';

  } catch (error) {
    console.error('🔍 [文本清理] 处理失败:', error);
    return '识别到食物图片，正在进行营养分析。';
  }
}

/**
 * 调试通义千问VL-MAX API响应结构
 * @param {string} imagePath 图片路径
 * @returns {Promise<object>} 原始响应数据
 */
export async function debugQwenVLResponse(imagePath) {
  try {
    console.log('🔍 [调试] 开始调试通义千问VL-MAX API响应结构...');

    const base64Image = await getBase64(imagePath);
    const requestData = {
      model: QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: "请简单描述这张图片中的食物。"
              },
              {
                image: `data:image/jpeg;base64,${base64Image}`
              }
            ]
          }
        ]
      },
      parameters: {
        result_format: "message"
      }
    };

    const response = await uni.request({
      url: QWEN_VL_CONFIG.BASE_URL,
      method: 'POST',
      data: requestData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: QWEN_VL_CONFIG.TIMEOUT
    });

    console.log('🔍 [调试] 完整的API响应:', JSON.stringify(response, null, 2));
    return response;

  } catch (error) {
    console.error('🔍 [调试] 调试失败:', error);
    throw error;
  }
}

/**
 * 测试通义千问VL-MAX API连接
 * @returns {Promise<boolean>} 测试结果
 */
export async function testQwenVLConnection() {
  try {
    console.log('🔍 [通义千问VL-MAX] 测试API连接...');

    // 创建一个简单的测试请求
    const testData = {
      model: QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: "测试连接"
              }
            ]
          }
        ]
      }
    };

    const response = await uni.request({
      url: QWEN_VL_CONFIG.BASE_URL,
      method: 'POST',
      data: testData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: 10000
    });

    console.log('🔍 [通义千问VL-MAX] 连接测试结果:', response.statusCode);
    return response.statusCode === 200;

  } catch (error) {
    console.error('🔍 [通义千问VL-MAX] 连接测试失败:', error);
    return false;
  }
}

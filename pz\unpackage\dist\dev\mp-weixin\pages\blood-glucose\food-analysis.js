"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_ai_service = require("../../utils/ai/service.js");
const utils_ai_qwenVLService = require("../../utils/ai/qwenVLService.js");
if (!Math) {
  navbar();
}
const navbar = () => "../../components/navbar/navbar.js";
const _sfc_main = {
  __name: "food-analysis",
  setup(__props) {
    const selectedImage = common_vendor.ref("");
    const analyzing = common_vendor.ref(false);
    const analysisResult = common_vendor.ref(null);
    const historyRecords = common_vendor.ref([]);
    const currentStep = common_vendor.ref("");
    const errorMessage = common_vendor.ref("");
    const contentStyle = common_vendor.computed(() => {
      common_vendor.index.getSystemInfoSync();
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      const navHeight = menuButtonInfo.top + menuButtonInfo.height + 8;
      return {
        paddingTop: navHeight + "px"
      };
    });
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          selectedImage.value = res.tempFilePaths[0];
          analysisResult.value = null;
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:234", "图片选择成功:", res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:237", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    };
    const reSelectImage = () => {
      selectedImage.value = "";
      analysisResult.value = null;
    };
    const analyzeFood = async () => {
      if (!selectedImage.value) {
        common_vendor.index.showToast({
          title: "请先选择图片",
          icon: "none"
        });
        return;
      }
      analyzing.value = true;
      try {
        errorMessage.value = "";
        currentStep.value = "正在检查图片质量...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:270", "开始图片质量检查...");
        try {
          const fileInfo = await new Promise((resolve, reject) => {
            common_vendor.index.getFileInfo({
              filePath: selectedImage.value,
              success: resolve,
              fail: reject
            });
          });
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:282", "图片文件信息:", fileInfo);
          if (fileInfo.size < 1e4) {
            common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:286", "图片文件过小，可能质量不佳");
            common_vendor.index.showToast({
              title: "图片质量可能不佳，建议重新拍摄",
              icon: "none",
              duration: 2e3
            });
          } else if (fileInfo.size > 1e7) {
            common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:293", "图片文件过大");
            common_vendor.index.showToast({
              title: "图片文件较大，处理可能较慢",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (fileError) {
          common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:301", "🔍 [食物识别] 无法获取文件信息:", fileError);
        }
        currentStep.value = "正在识别食物...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:306", "🔍 [食物识别] ===== 第一步：通义千问VL-MAX图像识别 =====");
        common_vendor.index.showLoading({
          title: "正在识别食物...",
          mask: true
        });
        const qwenDescription = await utils_ai_qwenVLService.analyzeImageWithQwenVL(selectedImage.value);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:313", "🔍 [食物识别] 通义千问VL-MAX识别结果:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:314", qwenDescription);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:315", "🔍 [食物识别] ===== 通义千问VL-MAX识别完成 =====");
        currentStep.value = "正在分析营养成分...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:319", "🔍 [食物识别] ===== 第二步：DeepSeek结构化处理 =====");
        common_vendor.index.showLoading({
          title: "正在分析营养成分...",
          mask: true
        });
        const deepSeekPrompt = `作为专业的营养师，请基于以下详细的食物图像描述，分析食物的营养成分和健康建议：

${qwenDescription}

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "食物名称",
  "confidence": 85,
  "giValue": 65,
  "nutrition": {
    "calories": 180,
    "carbs": 35,
    "protein": 8,
    "fat": 6,
    "fiber": 4,
    "sugar": 2
  },
  "healthAdvice": [
    "健康建议1",
    "健康建议2",
    "健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "推荐食用的食物及原因"
    ],
    "limitEat": [
      "需要限制的食物及原因"
    ],
    "suggestions": [
      "具体的饮食建议"
    ]
  }
}

要求：
1. 所有数值必须是合理的营养数据
2. GI值范围在0-100之间
3. 健康建议要针对糖尿病患者
4. 分析要专业且实用
5. 返回纯JSON格式，不要markdown代码块`;
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:368", "🔍 [食物识别] 发送给DeepSeek的请求内容:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:369", deepSeekPrompt);
        const deepSeekResponse = await utils_ai_service.sendMessageToAI(deepSeekPrompt);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:372", "🔍 [食物识别] DeepSeek返回的结果:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:373", deepSeekResponse);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:374", "🔍 [食物识别] ===== DeepSeek处理完成 =====");
        let parsedResult;
        try {
          const cleanedResponse = deepSeekResponse.replace(/```json\s*|\s*```/g, "").trim();
          parsedResult = JSON.parse(cleanedResponse);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:382", "🔍 [食物识别] JSON解析成功:", parsedResult);
        } catch (parseError) {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:384", "🔍 [食物识别] JSON解析失败:", parseError);
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:385", "🔍 [食物识别] 原始响应内容:", deepSeekResponse);
          parsedResult = {
            foodName: "识别的食物",
            confidence: 80,
            giValue: 60,
            nutrition: {
              calories: 150,
              carbs: 30,
              protein: 6,
              fat: 5,
              fiber: 3,
              sugar: 8
            },
            healthAdvice: [
              "请注意控制食用份量",
              "建议搭配低GI食物一起食用",
              "餐后适当运动有助于血糖控制"
            ],
            detailedAnalysis: {
              canEat: ["根据识别结果，适量食用有益健康"],
              limitEat: ["注意控制高糖高脂食物的摄入"],
              suggestions: ["建议咨询专业营养师制定个性化饮食方案"]
            }
          };
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:410", "🔍 [食物识别] 使用备用数据结构:", parsedResult);
        }
        analysisResult.value = parsedResult;
        const newRecord = {
          id: Date.now(),
          image: selectedImage.value,
          foodName: analysisResult.value.foodName,
          giValue: analysisResult.value.giValue,
          timestamp: Date.now(),
          confidence: analysisResult.value.confidence,
          nutrition: analysisResult.value.nutrition,
          healthAdvice: analysisResult.value.healthAdvice,
          detailedAnalysis: analysisResult.value.detailedAnalysis,
          date: (/* @__PURE__ */ new Date()).toLocaleDateString("zh-CN", {
            month: "numeric",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit"
          })
        };
        saveHistoryRecord(newRecord);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "分析完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:443", "🔍 [食物识别] 分析失败:", error);
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:444", "🔍 [食物识别] 错误详情:", {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        common_vendor.index.hideLoading();
        let errorTitle = "分析失败，请重试";
        if (error.message && error.message.includes("网络")) {
          errorTitle = "网络连接失败，请检查网络";
          errorMessage.value = "请检查网络连接后重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:456", "🔍 [食物识别] 网络错误:", error.message);
        } else if (error.message && error.message.includes("通义千问")) {
          errorTitle = "图片识别失败";
          errorMessage.value = "请选择清晰的食物图片重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:460", "🔍 [食物识别] 通义千问VL-MAX错误:", error.message);
        } else if (error.message && error.message.includes("AI服务")) {
          errorTitle = "AI分析服务暂时不可用";
          errorMessage.value = "服务暂时繁忙，请稍后重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:464", "🔍 [食物识别] DeepSeek API错误:", error.message);
        } else {
          errorMessage.value = "分析过程中出现错误，请重新尝试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:467", "🔍 [食物识别] 未知错误:", error.message);
        }
        common_vendor.index.showToast({
          title: errorTitle,
          icon: "none",
          duration: 3e3
        });
      } finally {
        analyzing.value = false;
        currentStep.value = "";
      }
    };
    const getGIClass = (giValue) => {
      if (giValue <= 55)
        return "low";
      if (giValue <= 70)
        return "medium";
      return "high";
    };
    const getGIDescription = (giValue) => {
      if (giValue <= 55)
        return "低GI食物，对血糖影响较小，适合糖尿病患者食用";
      if (giValue <= 70)
        return "中等GI食物，适量食用，建议搭配低GI食物";
      return "高GI食物，会快速升高血糖，糖尿病患者需严格控制";
    };
    const saveAnalysisRecord = () => {
      if (!analysisResult.value)
        return;
      const record = {
        id: Date.now(),
        image: selectedImage.value,
        foodName: analysisResult.value.foodName,
        confidence: analysisResult.value.confidence,
        giValue: analysisResult.value.giValue,
        nutrition: analysisResult.value.nutrition,
        healthAdvice: analysisResult.value.healthAdvice,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      const existingRecords = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      existingRecords.unshift(record);
      common_vendor.index.setStorageSync("foodAnalysisRecords", existingRecords);
      historyRecords.value = existingRecords;
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
    };
    const loadHistoryRecords = () => {
      const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      historyRecords.value = records;
    };
    const saveHistoryRecord = (record) => {
      try {
        const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
        records.unshift(record);
        if (records.length > 50) {
          records.splice(50);
        }
        common_vendor.index.setStorageSync("foodAnalysisRecords", records);
        historyRecords.value = records;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:602", "保存历史记录失败:", error);
      }
    };
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${month}月${day}日 ${hours}:${minutes}`;
    };
    const viewHistoryDetail = (record) => {
      analysisResult.value = record;
      selectedImage.value = record.image;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/blood-glucose/food-history"
      });
    };
    common_vendor.onMounted(() => {
      loadHistoryRecords();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isHomePage: false,
          title: "饮食分析"
        }),
        b: selectedImage.value
      }, selectedImage.value ? {
        c: selectedImage.value
      } : {
        d: common_assets._imports_0$8
      }, {
        e: common_vendor.o(chooseImage),
        f: selectedImage.value
      }, selectedImage.value ? {
        g: common_vendor.o(reSelectImage),
        h: common_vendor.t(analyzing.value ? "分析中..." : "开始分析"),
        i: common_vendor.o(analyzeFood),
        j: analyzing.value
      } : {}, {
        k: analyzing.value || errorMessage.value
      }, analyzing.value || errorMessage.value ? common_vendor.e({
        l: analyzing.value
      }, analyzing.value ? {
        m: common_vendor.t(currentStep.value)
      } : {}, {
        n: errorMessage.value
      }, errorMessage.value ? {
        o: common_vendor.t(errorMessage.value)
      } : {}) : {}, {
        p: analysisResult.value
      }, analysisResult.value ? common_vendor.e({
        q: common_vendor.t(analysisResult.value.foodName),
        r: common_vendor.t(analysisResult.value.confidence),
        s: common_vendor.t(analysisResult.value.nutrition.calories),
        t: common_vendor.t(analysisResult.value.nutrition.carbs),
        v: common_vendor.t(analysisResult.value.nutrition.protein),
        w: common_vendor.t(analysisResult.value.nutrition.fat),
        x: common_vendor.t(analysisResult.value.nutrition.fiber),
        y: common_vendor.t(analysisResult.value.nutrition.sugar),
        z: common_vendor.t(analysisResult.value.giValue),
        A: common_vendor.n(getGIClass(analysisResult.value.giValue)),
        B: common_vendor.t(getGIDescription(analysisResult.value.giValue)),
        C: analysisResult.value.detailedAnalysis
      }, analysisResult.value.detailedAnalysis ? {
        D: common_assets._imports_1$8,
        E: common_vendor.t(analysisResult.value.healthAdvice.join("；")),
        F: common_vendor.t(analysisResult.value.detailedAnalysis.canEat.join("；")),
        G: common_vendor.t(analysisResult.value.detailedAnalysis.limitEat.join("；")),
        H: common_vendor.t(analysisResult.value.detailedAnalysis.suggestions.join("；"))
      } : {}, {
        I: common_vendor.o(saveAnalysisRecord)
      }) : {}, {
        J: historyRecords.value.length > 0
      }, historyRecords.value.length > 0 ? {
        K: common_vendor.o(viewAllHistory),
        L: common_vendor.f(historyRecords.value.slice(0, 3), (record, k0, i0) => {
          return {
            a: record.image,
            b: common_vendor.t(record.foodName),
            c: common_vendor.t(formatDate(record.timestamp)),
            d: common_vendor.t(record.giValue),
            e: common_vendor.n(getGIClass(record.giValue)),
            f: record.id,
            g: common_vendor.o(($event) => viewHistoryDetail(record), record.id)
          };
        })
      } : {}, {
        M: common_vendor.s(contentStyle.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e60890c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/blood-glucose/food-analysis.js.map

"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_ai_service = require("../../utils/ai/service.js");
const utils_ai_qwenVLService = require("../../utils/ai/qwenVLService.js");
if (!Math) {
  navbar();
}
const navbar = () => "../../components/navbar/navbar.js";
const _sfc_main = {
  __name: "food-analysis",
  setup(__props) {
    const selectedImage = common_vendor.ref("");
    const analyzing = common_vendor.ref(false);
    const analysisResult = common_vendor.ref(null);
    const historyRecords = common_vendor.ref([]);
    const currentStep = common_vendor.ref("");
    const errorMessage = common_vendor.ref("");
    const contentStyle = common_vendor.computed(() => {
      common_vendor.index.getSystemInfoSync();
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      const navHeight = menuButtonInfo.top + menuButtonInfo.height + 8;
      return {
        paddingTop: navHeight + "px"
      };
    });
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          selectedImage.value = res.tempFilePaths[0];
          analysisResult.value = null;
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:234", "图片选择成功:", res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:237", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    };
    const reSelectImage = () => {
      selectedImage.value = "";
      analysisResult.value = null;
    };
    const analyzeFood = async () => {
      if (!selectedImage.value) {
        common_vendor.index.showToast({
          title: "请先选择图片",
          icon: "none"
        });
        return;
      }
      analyzing.value = true;
      try {
        errorMessage.value = "";
        currentStep.value = "正在检查图片质量...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:270", "开始图片质量检查...");
        try {
          const fileInfo = await new Promise((resolve, reject) => {
            common_vendor.index.getFileInfo({
              filePath: selectedImage.value,
              success: resolve,
              fail: reject
            });
          });
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:282", "图片文件信息:", fileInfo);
          if (fileInfo.size < 1e4) {
            common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:286", "图片文件过小，可能质量不佳");
            common_vendor.index.showToast({
              title: "图片质量可能不佳，建议重新拍摄",
              icon: "none",
              duration: 2e3
            });
          } else if (fileInfo.size > 1e7) {
            common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:293", "图片文件过大");
            common_vendor.index.showToast({
              title: "图片文件较大，处理可能较慢",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (fileError) {
          common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:301", "🔍 [食物识别] 无法获取文件信息:", fileError);
        }
        currentStep.value = "正在识别食物...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:306", "🔍 [食物识别] ===== 第一步：通义千问VL-MAX图像识别 =====");
        common_vendor.index.showLoading({
          title: "正在识别食物...",
          mask: true
        });
        const qwenDescription = await utils_ai_qwenVLService.analyzeImageWithQwenVL(selectedImage.value);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:313", "🔍 [食物识别] 通义千问VL-MAX识别结果:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:314", qwenDescription);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:315", "🔍 [食物识别] 识别结果类型:", typeof qwenDescription);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:316", "🔍 [食物识别] 识别结果长度:", qwenDescription ? qwenDescription.length : 0);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:317", "🔍 [食物识别] ===== 通义千问VL-MAX识别完成 =====");
        if (!qwenDescription || qwenDescription.trim().length === 0) {
          throw new Error("通义千问VL-MAX识别失败：返回空结果");
        }
        currentStep.value = "正在分析营养成分...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:326", "🔍 [食物识别] ===== 第二步：DeepSeek结构化处理 =====");
        common_vendor.index.showLoading({
          title: "正在分析营养成分...",
          mask: true
        });
        const deepSeekPrompt = `你是一位专业的营养师和糖尿病饮食专家。请仔细分析以下通义千问VL-MAX提供的详细食物图像描述，并基于这个具体的食物描述进行营养分析。

=== 食物图像识别结果 ===
${qwenDescription}
=== 识别结果结束 ===

请基于上述具体的食物识别结果，进行专业的营养成分分析和糖尿病饮食建议。请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "食物名称",
  "confidence": 85,
  "giValue": 65,
  "nutrition": {
    "calories": 180,
    "carbs": 35,
    "protein": 8,
    "fat": 6,
    "fiber": 4,
    "sugar": 2
  },
  "healthAdvice": [
    "健康建议1",
    "健康建议2",
    "健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "推荐食用的食物及原因"
    ],
    "limitEat": [
      "需要限制的食物及原因"
    ],
    "suggestions": [
      "具体的饮食建议"
    ]
  }
}

分析要求：
1. 必须基于上述具体的食物识别结果进行分析，不要使用通用数据
2. 所有数值必须是合理的营养数据，符合实际食物特征
3. GI值范围在0-100之间，要准确反映食物的血糖影响
4. 健康建议要针对糖尿病患者，结合具体食物特点
5. foodName要与识别结果中的主要食物一致
6. 分析要专业且实用，避免泛泛而谈
7. 返回纯JSON格式，不要markdown代码块或其他格式`;
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:380", "🔍 [食物识别] ===== 数据传递验证 =====");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:381", "🔍 [食物识别] qwenDescription变量值:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:382", qwenDescription);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:383", "🔍 [食物识别] prompt中是否包含识别结果:", deepSeekPrompt.includes(qwenDescription));
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:384", "🔍 [食物识别] 完整的DeepSeek请求内容:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:385", deepSeekPrompt);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:386", "🔍 [食物识别] ===== 发送请求到DeepSeek =====");
        const deepSeekResponse = await utils_ai_service.sendMessageToAI(deepSeekPrompt);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:389", "🔍 [食物识别] ===== DeepSeek响应接收 =====");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:390", "🔍 [食物识别] DeepSeek返回的原始结果:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:391", deepSeekResponse);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:392", "🔍 [食物识别] 响应类型:", typeof deepSeekResponse);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:393", "🔍 [食物识别] 响应长度:", deepSeekResponse ? deepSeekResponse.length : 0);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:394", "🔍 [食物识别] ===== DeepSeek处理完成 =====");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:397", "🔍 [食物识别] ===== 第三步：JSON解析处理 =====");
        let parsedResult;
        try {
          const cleanedResponse = deepSeekResponse.replace(/```json\s*|\s*```/g, "").trim();
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:402", "🔍 [食物识别] 清理后的响应内容:");
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:403", cleanedResponse);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:404", "🔍 [食物识别] 开始JSON解析...");
          parsedResult = JSON.parse(cleanedResponse);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:407", "🔍 [食物识别] ===== JSON解析成功 =====");
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:408", "🔍 [食物识别] 解析后的结果:");
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:409", parsedResult);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:412", "🔍 [食物识别] 结果验证:");
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:413", "🔍 [食物识别] - foodName:", parsedResult.foodName);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:414", "🔍 [食物识别] - confidence:", parsedResult.confidence);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:415", "🔍 [食物识别] - giValue:", parsedResult.giValue);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:416", "🔍 [食物识别] - nutrition:", parsedResult.nutrition);
        } catch (parseError) {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:419", "🔍 [食物识别] ===== JSON解析失败 =====");
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:420", "🔍 [食物识别] 解析错误:", parseError);
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:421", "🔍 [食物识别] 错误详情:", parseError.message);
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:422", "🔍 [食物识别] 原始响应内容:", deepSeekResponse);
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:423", "🔍 [食物识别] 清理后内容:", deepSeekResponse.replace(/```json\s*|\s*```/g, "").trim());
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:425", "🔍 [食物识别] 生成备用数据结构...");
          let extractedFoodName = "识别的食物";
          if (qwenDescription && qwenDescription.length > 0) {
            const foodKeywords = ["米饭", "面条", "苹果", "香蕉", "鸡蛋", "牛奶", "面包", "蔬菜", "肉类", "鱼类"];
            for (const keyword of foodKeywords) {
              if (qwenDescription.includes(keyword)) {
                extractedFoodName = keyword;
                break;
              }
            }
          }
          parsedResult = {
            foodName: extractedFoodName,
            confidence: 75,
            // 降低置信度，因为是备用数据
            giValue: 60,
            nutrition: {
              calories: 150,
              carbs: 30,
              protein: 6,
              fat: 5,
              fiber: 3,
              sugar: 8
            },
            healthAdvice: [
              `基于图像识别，${extractedFoodName}需要注意控制食用份量`,
              "建议搭配低GI食物一起食用",
              "餐后适当运动有助于血糖控制"
            ],
            detailedAnalysis: {
              canEat: [`根据识别结果，${extractedFoodName}适量食用有益健康`],
              limitEat: ["注意控制高糖高脂食物的摄入"],
              suggestions: ["由于AI分析服务暂时不可用，建议咨询专业营养师获取准确的饮食建议"]
            }
          };
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:463", "🔍 [食物识别] 备用数据结构生成完成:", parsedResult);
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:464", "🔍 [食物识别] 提取的食物名称:", extractedFoodName);
        }
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:468", "🔍 [食物识别] ===== 设置最终分析结果 =====");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:469", "🔍 [食物识别] 最终结果数据:");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:470", parsedResult);
        const requiredFields = ["foodName", "confidence", "giValue", "nutrition", "healthAdvice", "detailedAnalysis"];
        const missingFields = requiredFields.filter((field) => !parsedResult[field]);
        if (missingFields.length > 0) {
          common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:476", "🔍 [食物识别] 警告：结果缺少必要字段:", missingFields);
        }
        analysisResult.value = parsedResult;
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:480", "🔍 [食物识别] 分析结果已设置到响应式变量");
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:481", "🔍 [食物识别] analysisResult.value:", analysisResult.value);
        const newRecord = {
          id: Date.now(),
          image: selectedImage.value,
          foodName: analysisResult.value.foodName,
          giValue: analysisResult.value.giValue,
          timestamp: Date.now(),
          confidence: analysisResult.value.confidence,
          nutrition: analysisResult.value.nutrition,
          healthAdvice: analysisResult.value.healthAdvice,
          detailedAnalysis: analysisResult.value.detailedAnalysis,
          date: (/* @__PURE__ */ new Date()).toLocaleDateString("zh-CN", {
            month: "numeric",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit"
          })
        };
        saveHistoryRecord(newRecord);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "分析完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:510", "🔍 [食物识别] 分析失败:", error);
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:511", "🔍 [食物识别] 错误详情:", {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        common_vendor.index.hideLoading();
        let errorTitle = "分析失败，请重试";
        if (error.message && error.message.includes("网络")) {
          errorTitle = "网络连接失败，请检查网络";
          errorMessage.value = "请检查网络连接后重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:523", "🔍 [食物识别] 网络错误:", error.message);
        } else if (error.message && error.message.includes("通义千问")) {
          errorTitle = "图片识别失败";
          errorMessage.value = "请选择清晰的食物图片重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:527", "🔍 [食物识别] 通义千问VL-MAX错误:", error.message);
        } else if (error.message && error.message.includes("AI服务")) {
          errorTitle = "AI分析服务暂时不可用";
          errorMessage.value = "服务暂时繁忙，请稍后重试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:531", "🔍 [食物识别] DeepSeek API错误:", error.message);
        } else {
          errorMessage.value = "分析过程中出现错误，请重新尝试";
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:534", "🔍 [食物识别] 未知错误:", error.message);
        }
        common_vendor.index.showToast({
          title: errorTitle,
          icon: "none",
          duration: 3e3
        });
      } finally {
        analyzing.value = false;
        currentStep.value = "";
      }
    };
    const getGIClass = (giValue) => {
      if (giValue <= 55)
        return "low";
      if (giValue <= 70)
        return "medium";
      return "high";
    };
    const getGIDescription = (giValue) => {
      if (giValue <= 55)
        return "低GI食物，对血糖影响较小，适合糖尿病患者食用";
      if (giValue <= 70)
        return "中等GI食物，适量食用，建议搭配低GI食物";
      return "高GI食物，会快速升高血糖，糖尿病患者需严格控制";
    };
    const saveAnalysisRecord = () => {
      if (!analysisResult.value)
        return;
      const record = {
        id: Date.now(),
        image: selectedImage.value,
        foodName: analysisResult.value.foodName,
        confidence: analysisResult.value.confidence,
        giValue: analysisResult.value.giValue,
        nutrition: analysisResult.value.nutrition,
        healthAdvice: analysisResult.value.healthAdvice,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      const existingRecords = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      existingRecords.unshift(record);
      common_vendor.index.setStorageSync("foodAnalysisRecords", existingRecords);
      historyRecords.value = existingRecords;
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
    };
    const loadHistoryRecords = () => {
      const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      historyRecords.value = records;
    };
    const saveHistoryRecord = (record) => {
      try {
        const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
        records.unshift(record);
        if (records.length > 50) {
          records.splice(50);
        }
        common_vendor.index.setStorageSync("foodAnalysisRecords", records);
        historyRecords.value = records;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:669", "保存历史记录失败:", error);
      }
    };
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${month}月${day}日 ${hours}:${minutes}`;
    };
    const viewHistoryDetail = (record) => {
      analysisResult.value = record;
      selectedImage.value = record.image;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/blood-glucose/food-history"
      });
    };
    common_vendor.onMounted(() => {
      loadHistoryRecords();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isHomePage: false,
          title: "饮食分析"
        }),
        b: selectedImage.value
      }, selectedImage.value ? {
        c: selectedImage.value
      } : {
        d: common_assets._imports_0$8
      }, {
        e: common_vendor.o(chooseImage),
        f: selectedImage.value
      }, selectedImage.value ? {
        g: common_vendor.o(reSelectImage),
        h: common_vendor.t(analyzing.value ? "分析中..." : "开始分析"),
        i: common_vendor.o(analyzeFood),
        j: analyzing.value
      } : {}, {
        k: analyzing.value || errorMessage.value
      }, analyzing.value || errorMessage.value ? common_vendor.e({
        l: analyzing.value
      }, analyzing.value ? {
        m: common_vendor.t(currentStep.value)
      } : {}, {
        n: errorMessage.value
      }, errorMessage.value ? {
        o: common_vendor.t(errorMessage.value)
      } : {}) : {}, {
        p: analysisResult.value
      }, analysisResult.value ? common_vendor.e({
        q: common_vendor.t(analysisResult.value.foodName),
        r: common_vendor.t(analysisResult.value.confidence),
        s: common_vendor.t(analysisResult.value.nutrition.calories),
        t: common_vendor.t(analysisResult.value.nutrition.carbs),
        v: common_vendor.t(analysisResult.value.nutrition.protein),
        w: common_vendor.t(analysisResult.value.nutrition.fat),
        x: common_vendor.t(analysisResult.value.nutrition.fiber),
        y: common_vendor.t(analysisResult.value.nutrition.sugar),
        z: common_vendor.t(analysisResult.value.giValue),
        A: common_vendor.n(getGIClass(analysisResult.value.giValue)),
        B: common_vendor.t(getGIDescription(analysisResult.value.giValue)),
        C: analysisResult.value.detailedAnalysis
      }, analysisResult.value.detailedAnalysis ? {
        D: common_assets._imports_1$8,
        E: common_vendor.t(analysisResult.value.healthAdvice.join("；")),
        F: common_vendor.t(analysisResult.value.detailedAnalysis.canEat.join("；")),
        G: common_vendor.t(analysisResult.value.detailedAnalysis.limitEat.join("；")),
        H: common_vendor.t(analysisResult.value.detailedAnalysis.suggestions.join("；"))
      } : {}, {
        I: common_vendor.o(saveAnalysisRecord)
      }) : {}, {
        J: historyRecords.value.length > 0
      }, historyRecords.value.length > 0 ? {
        K: common_vendor.o(viewAllHistory),
        L: common_vendor.f(historyRecords.value.slice(0, 3), (record, k0, i0) => {
          return {
            a: record.image,
            b: common_vendor.t(record.foodName),
            c: common_vendor.t(formatDate(record.timestamp)),
            d: common_vendor.t(record.giValue),
            e: common_vendor.n(getGIClass(record.giValue)),
            f: record.id,
            g: common_vendor.o(($event) => viewHistoryDetail(record), record.id)
          };
        })
      } : {}, {
        M: common_vendor.s(contentStyle.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e60890c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/blood-glucose/food-analysis.js.map

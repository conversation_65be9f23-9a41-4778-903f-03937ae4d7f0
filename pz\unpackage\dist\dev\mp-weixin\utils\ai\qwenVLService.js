"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_ai_config = require("./config.js");
function getBase64(filePath) {
  return new Promise((resolve, reject) => {
    common_vendor.index.getFileSystemManager().readFile({
      filePath,
      encoding: "base64",
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/ai/qwenVLService.js:18", "读取图片文件失败:", err);
        reject(new Error("读取图片文件失败"));
      }
    });
  });
}
async function analyzeImageWithQwenVL(imagePath) {
  var _a, _b, _c, _d;
  try {
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:32", "🔍 [通义千问VL-MAX] 开始分析食物图片...");
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:33", "🔍 [通义千问VL-MAX] 图片路径:", imagePath);
    const base64Image = await getBase64(imagePath);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:37", "🔍 [通义千问VL-MAX] 图片base64编码获取成功，长度:", base64Image.length);
    const requestData = {
      model: utils_ai_config.QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: `请详细分析这张食物图片，包括以下信息：
1. 食物种类和名称（尽可能具体）
2. 食物的数量和大小
3. 食物的外观特征（颜色、形状、质地等）
4. 烹饪方式（如炒、煮、蒸、烤等）
5. 配菜和搭配情况
6. 食物的新鲜程度和品质
7. 估计的份量大小
8. 可能的营养特点

请用中文详细描述，描述越详细越好，这将用于后续的营养分析。`
              },
              {
                image: `data:image/jpeg;base64,${base64Image}`
              }
            ]
          }
        ]
      },
      parameters: {
        result_format: "message"
      }
    };
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:72", "🔍 [通义千问VL-MAX] 发送请求到API...");
    const response = await common_vendor.index.request({
      url: utils_ai_config.QWEN_VL_CONFIG.BASE_URL,
      method: "POST",
      data: requestData,
      header: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${utils_ai_config.QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: utils_ai_config.QWEN_VL_CONFIG.TIMEOUT
    });
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:86", "🔍 [通义千问VL-MAX] API响应状态:", response.statusCode);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:87", "🔍 [通义千问VL-MAX] API响应数据:", response.data);
    if (response.statusCode !== 200) {
      throw new Error(`通义千问VL-MAX API请求失败: HTTP ${response.statusCode}`);
    }
    const responseData = response.data;
    if (!responseData || !responseData.output) {
      throw new Error(`通义千问VL-MAX API返回错误: ${(responseData == null ? void 0 : responseData.message) || "未知错误"}`);
    }
    const imageDescription = (_d = (_c = (_b = (_a = responseData.output) == null ? void 0 : _a.choices) == null ? void 0 : _b[0]) == null ? void 0 : _c.message) == null ? void 0 : _d.content;
    if (!imageDescription) {
      throw new Error("通义千问VL-MAX API返回的数据格式不正确");
    }
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:106", "🔍 [通义千问VL-MAX] 图像识别成功");
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:107", "🔍 [通义千问VL-MAX] 识别结果类型:", typeof imageDescription);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:108", "🔍 [通义千问VL-MAX] 识别结果:", imageDescription);
    const result = typeof imageDescription === "string" ? imageDescription : String(imageDescription);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:112", "🔍 [通义千问VL-MAX] 最终返回结果类型:", typeof result);
    return result;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/ai/qwenVLService.js:117", "🔍 [通义千问VL-MAX] 图像识别失败:", error);
    if (error.message.includes("网络")) {
      throw new Error("网络连接失败，请检查网络设置后重试");
    } else if (error.message.includes("API")) {
      throw new Error("图像识别服务暂时不可用，请稍后重试");
    } else {
      throw new Error(`图像识别失败: ${error.message}`);
    }
  }
}
exports.analyzeImageWithQwenVL = analyzeImageWithQwenVL;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/utils/ai/qwenVLService.js.map

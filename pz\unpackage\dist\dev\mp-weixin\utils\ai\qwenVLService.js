"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_ai_config = require("./config.js");
function getBase64(filePath) {
  return new Promise((resolve, reject) => {
    common_vendor.index.getFileSystemManager().readFile({
      filePath,
      encoding: "base64",
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/ai/qwenVLService.js:18", "读取图片文件失败:", err);
        reject(new Error("读取图片文件失败"));
      }
    });
  });
}
async function analyzeImageWithQwenVL(imagePath) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
  try {
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:32", "🔍 [通义千问VL-MAX] 开始分析食物图片...");
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:33", "🔍 [通义千问VL-MAX] 图片路径:", imagePath);
    const base64Image = await getBase64(imagePath);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:37", "🔍 [通义千问VL-MAX] 图片base64编码获取成功，长度:", base64Image.length);
    const requestData = {
      model: utils_ai_config.QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: `请详细分析这张食物图片，包括以下信息：
1. 食物种类和名称（尽可能具体）
2. 食物的数量和大小
3. 食物的外观特征（颜色、形状、质地等）
4. 烹饪方式（如炒、煮、蒸、烤等）
5. 配菜和搭配情况
6. 食物的新鲜程度和品质
7. 估计的份量大小
8. 可能的营养特点

请用中文详细描述，描述越详细越好，这将用于后续的营养分析。`
              },
              {
                image: `data:image/jpeg;base64,${base64Image}`
              }
            ]
          }
        ]
      },
      parameters: {
        result_format: "message"
      }
    };
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:72", "🔍 [通义千问VL-MAX] 发送请求到API...");
    const response = await common_vendor.index.request({
      url: utils_ai_config.QWEN_VL_CONFIG.BASE_URL,
      method: "POST",
      data: requestData,
      header: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${utils_ai_config.QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: utils_ai_config.QWEN_VL_CONFIG.TIMEOUT
    });
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:86", "🔍 [通义千问VL-MAX] API响应状态:", response.statusCode);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:87", "🔍 [通义千问VL-MAX] API响应数据完整结构:", JSON.stringify(response.data, null, 2));
    if (response.statusCode !== 200) {
      throw new Error(`通义千问VL-MAX API请求失败: HTTP ${response.statusCode}`);
    }
    const responseData = response.data;
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:96", "🔍 [通义千问VL-MAX] responseData:", responseData);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:97", "🔍 [通义千问VL-MAX] responseData.output:", responseData == null ? void 0 : responseData.output);
    if (!responseData || !responseData.output) {
      throw new Error(`通义千问VL-MAX API返回错误: ${(responseData == null ? void 0 : responseData.message) || "未知错误"}`);
    }
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:104", "🔍 [通义千问VL-MAX] output.choices:", (_a = responseData.output) == null ? void 0 : _a.choices);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:105", "🔍 [通义千问VL-MAX] choices[0]:", (_c = (_b = responseData.output) == null ? void 0 : _b.choices) == null ? void 0 : _c[0]);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:106", "🔍 [通义千问VL-MAX] choices[0].message:", (_f = (_e = (_d = responseData.output) == null ? void 0 : _d.choices) == null ? void 0 : _e[0]) == null ? void 0 : _f.message);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:107", "🔍 [通义千问VL-MAX] choices[0].message.content:", (_j = (_i = (_h = (_g = responseData.output) == null ? void 0 : _g.choices) == null ? void 0 : _h[0]) == null ? void 0 : _i.message) == null ? void 0 : _j.content);
    const imageDescription = (_n = (_m = (_l = (_k = responseData.output) == null ? void 0 : _k.choices) == null ? void 0 : _l[0]) == null ? void 0 : _m.message) == null ? void 0 : _n.content;
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:111", "🔍 [通义千问VL-MAX] 提取的imageDescription:", imageDescription);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:112", "🔍 [通义千问VL-MAX] imageDescription类型:", typeof imageDescription);
    if (!imageDescription) {
      common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:116", "🔍 [通义千问VL-MAX] 尝试其他数据路径...");
      common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:117", "🔍 [通义千问VL-MAX] responseData.output.text:", (_o = responseData.output) == null ? void 0 : _o.text);
      common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:118", "🔍 [通义千问VL-MAX] responseData.output.content:", (_p = responseData.output) == null ? void 0 : _p.content);
      common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:119", "🔍 [通义千问VL-MAX] responseData.choices:", responseData == null ? void 0 : responseData.choices);
      throw new Error("通义千问VL-MAX API返回的数据格式不正确");
    }
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:124", "🔍 [通义千问VL-MAX] 图像识别成功");
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:125", "🔍 [通义千问VL-MAX] 识别结果类型:", typeof imageDescription);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:126", "🔍 [通义千问VL-MAX] 识别结果:", imageDescription);
    let finalResult = "";
    if (typeof imageDescription === "string") {
      finalResult = imageDescription;
    } else if (imageDescription && typeof imageDescription === "object") {
      common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:134", "🔍 [通义千问VL-MAX] imageDescription是对象，尝试提取文本...");
      finalResult = imageDescription.text || imageDescription.content || imageDescription.message || imageDescription.value || imageDescription.result;
      if (typeof finalResult === "object" && finalResult) {
        finalResult = finalResult.text || finalResult.content || finalResult.message || JSON.stringify(finalResult);
      }
      if (!finalResult || typeof finalResult !== "string") {
        common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:153", "🔍 [通义千问VL-MAX] 无法提取文本，使用JSON序列化");
        finalResult = JSON.stringify(imageDescription);
      }
    } else {
      finalResult = String(imageDescription || "");
    }
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:160", "🔍 [通义千问VL-MAX] 最终处理结果:", finalResult);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:161", "🔍 [通义千问VL-MAX] 最终结果类型:", typeof finalResult);
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:162", "🔍 [通义千问VL-MAX] 最终结果长度:", finalResult.length);
    return finalResult;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/ai/qwenVLService.js:167", "🔍 [通义千问VL-MAX] 图像识别失败:", error);
    if (error.message.includes("网络")) {
      throw new Error("网络连接失败，请检查网络设置后重试");
    } else if (error.message.includes("API")) {
      throw new Error("图像识别服务暂时不可用，请稍后重试");
    } else {
      throw new Error(`图像识别失败: ${error.message}`);
    }
  }
}
async function debugQwenVLResponse(imagePath) {
  try {
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:187", "🔍 [调试] 开始调试通义千问VL-MAX API响应结构...");
    const base64Image = await getBase64(imagePath);
    const requestData = {
      model: utils_ai_config.QWEN_VL_CONFIG.MODEL,
      input: {
        messages: [
          {
            role: "user",
            content: [
              {
                text: "请简单描述这张图片中的食物。"
              },
              {
                image: `data:image/jpeg;base64,${base64Image}`
              }
            ]
          }
        ]
      },
      parameters: {
        result_format: "message"
      }
    };
    const response = await common_vendor.index.request({
      url: utils_ai_config.QWEN_VL_CONFIG.BASE_URL,
      method: "POST",
      data: requestData,
      header: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${utils_ai_config.QWEN_VL_CONFIG.API_KEY}`
      },
      timeout: utils_ai_config.QWEN_VL_CONFIG.TIMEOUT
    });
    common_vendor.index.__f__("log", "at utils/ai/qwenVLService.js:223", "🔍 [调试] 完整的API响应:", JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/ai/qwenVLService.js:227", "🔍 [调试] 调试失败:", error);
    throw error;
  }
}
exports.analyzeImageWithQwenVL = analyzeImageWithQwenVL;
exports.debugQwenVLResponse = debugQwenVLResponse;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/utils/ai/qwenVLService.js.map

// DeepSeek API测试文件
// 专门测试DeepSeek API的连接和响应

import { sendMessageToAI } from './utils/ai/service.js';
import { DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL } from './utils/ai/config.js';

/**
 * 测试DeepSeek API基本连接
 */
async function testDeepSeekConnection() {
  console.log('🔍 [DeepSeek测试] ===== 开始测试DeepSeek API连接 =====');
  
  try {
    console.log('🔍 [DeepSeek测试] 配置信息:');
    console.log('  - API Key:', DEEPSEEK_API_KEY ? '已配置 ✓' : '未配置 ✗');
    console.log('  - Base URL:', DEEPSEEK_BASE_URL);
    
    console.log('🔍 [DeepSeek测试] 发送测试消息...');
    const testMessage = '你好，这是一个连接测试。请简单回复"连接成功"。';
    
    const response = await sendMessageToAI(testMessage);
    console.log('🔍 [DeepSeek测试] API响应成功:');
    console.log(response);
    
    console.log('✅ [DeepSeek测试] DeepSeek API连接测试成功');
    return { success: true, response };
    
  } catch (error) {
    console.error('❌ [DeepSeek测试] DeepSeek API连接测试失败:', error);
    console.error('🔍 [DeepSeek测试] 错误详情:', {
      message: error.message,
      stack: error.stack
    });
    return { success: false, error: error.message };
  }
}

/**
 * 测试DeepSeek营养分析功能
 */
async function testDeepSeekNutritionAnalysis() {
  console.log('🔍 [DeepSeek测试] ===== 开始测试营养分析功能 =====');
  
  try {
    const testDescription = `图片中显示了一碗白米饭，大约150克左右。米饭颗粒饱满，呈现白色，质地看起来比较软糯。这是一份标准的中式主食，通常作为正餐的主要碳水化合物来源。`;
    
    const nutritionPrompt = `作为专业的营养师，请基于以下详细的食物图像描述，分析食物的营养成分和健康建议：

${testDescription}

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "食物名称",
  "confidence": 85,
  "giValue": 65,
  "nutrition": {
    "calories": 180,
    "carbs": 35,
    "protein": 8,
    "fat": 6,
    "fiber": 4,
    "sugar": 2
  },
  "healthAdvice": [
    "健康建议1",
    "健康建议2",
    "健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "推荐食用的食物及原因"
    ],
    "limitEat": [
      "需要限制的食物及原因"
    ],
    "suggestions": [
      "具体的饮食建议"
    ]
  }
}

要求：
1. 所有数值必须是合理的营养数据
2. GI值范围在0-100之间
3. 健康建议要针对糖尿病患者
4. 分析要专业且实用
5. 返回纯JSON格式，不要markdown代码块`;

    console.log('🔍 [DeepSeek测试] 发送营养分析请求...');
    console.log('🔍 [DeepSeek测试] 请求内容:');
    console.log(nutritionPrompt);
    
    const response = await sendMessageToAI(nutritionPrompt);
    console.log('🔍 [DeepSeek测试] 营养分析响应:');
    console.log(response);
    
    // 尝试解析JSON
    try {
      const cleanedResponse = response.replace(/```json\s*|\s*```/g, '').trim();
      const parsedResult = JSON.parse(cleanedResponse);
      console.log('✅ [DeepSeek测试] JSON解析成功:');
      console.log(parsedResult);
      
      // 验证数据结构
      const isValidStructure = 
        parsedResult.foodName &&
        typeof parsedResult.confidence === 'number' &&
        typeof parsedResult.giValue === 'number' &&
        parsedResult.nutrition &&
        Array.isArray(parsedResult.healthAdvice) &&
        parsedResult.detailedAnalysis;
      
      if (isValidStructure) {
        console.log('✅ [DeepSeek测试] 数据结构验证通过');
        return { success: true, response, parsedResult };
      } else {
        console.error('❌ [DeepSeek测试] 数据结构验证失败');
        return { success: false, error: '数据结构不完整' };
      }
      
    } catch (parseError) {
      console.error('❌ [DeepSeek测试] JSON解析失败:', parseError);
      console.error('🔍 [DeepSeek测试] 原始响应:', response);
      return { success: false, error: 'JSON解析失败', response };
    }
    
  } catch (error) {
    console.error('❌ [DeepSeek测试] 营养分析测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试DeepSeek API的错误处理
 */
async function testDeepSeekErrorHandling() {
  console.log('🔍 [DeepSeek测试] ===== 开始测试错误处理 =====');
  
  try {
    // 发送一个可能导致错误的请求
    const invalidPrompt = '请返回一个非常长的响应，超过token限制' + 'x'.repeat(10000);
    
    console.log('🔍 [DeepSeek测试] 发送可能导致错误的请求...');
    const response = await sendMessageToAI(invalidPrompt);
    console.log('🔍 [DeepSeek测试] 意外成功响应:', response);
    
    return { success: true, response };
    
  } catch (error) {
    console.log('✅ [DeepSeek测试] 错误处理正常工作:', error.message);
    return { success: true, error: error.message };
  }
}

/**
 * 运行所有DeepSeek测试
 */
async function runAllDeepSeekTests() {
  console.log('🔍 [DeepSeek测试] ===== 开始运行所有DeepSeek测试 =====');
  
  const results = {
    connection: await testDeepSeekConnection(),
    nutrition: await testDeepSeekNutritionAnalysis(),
    errorHandling: await testDeepSeekErrorHandling()
  };
  
  console.log('🔍 [DeepSeek测试] ===== 所有测试完成 =====');
  console.log('🔍 [DeepSeek测试] 测试结果汇总:');
  console.log('  - 连接测试:', results.connection.success ? '✅ 通过' : '❌ 失败');
  console.log('  - 营养分析测试:', results.nutrition.success ? '✅ 通过' : '❌ 失败');
  console.log('  - 错误处理测试:', results.errorHandling.success ? '✅ 通过' : '❌ 失败');
  
  return results;
}

// 导出测试函数
export {
  testDeepSeekConnection,
  testDeepSeekNutritionAnalysis,
  testDeepSeekErrorHandling,
  runAllDeepSeekTests
};

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  // Node.js环境
  runAllDeepSeekTests().then(results => {
    console.log('🔍 [DeepSeek测试] 测试完成，结果:', results);
  }).catch(error => {
    console.error('🔍 [DeepSeek测试] 测试执行失败:', error);
  });
}

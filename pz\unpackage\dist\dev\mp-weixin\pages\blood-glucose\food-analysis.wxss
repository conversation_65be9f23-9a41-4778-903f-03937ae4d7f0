
.page-container.data-v-8e60890c {
		min-height: 100vh;
		background-color: #f5f7fa;
}
.content-container.data-v-8e60890c {
		padding: 20rpx;
}

	/* 上传区域 */
.upload-section.data-v-8e60890c {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
}
.upload-header.data-v-8e60890c {
		text-align: center;
		margin-bottom: 30rpx;
}
.upload-title.data-v-8e60890c {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 12rpx;
}
.upload-subtitle.data-v-8e60890c {
		display: block;
		font-size: 28rpx;
		color: #999;
}
.upload-area.data-v-8e60890c {
		border: 3rpx dashed #e0e0e0;
		border-radius: 16rpx;
		min-height: 400rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		transition: all 0.3s;
}
.upload-area.data-v-8e60890c:active {
		border-color: #00b38a;
		background: rgba(0, 179, 138, 0.05);
}
.preview-image.data-v-8e60890c {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
}
.upload-placeholder.data-v-8e60890c {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
}
.upload-icon.data-v-8e60890c {
		width: 80rpx;
		height: 80rpx;
		opacity: 0.6;
}
.upload-text.data-v-8e60890c {
		font-size: 32rpx;
		color: #666;
}
.upload-hint.data-v-8e60890c {
		font-size: 24rpx;
		color: #999;
}
.upload-actions.data-v-8e60890c {
		display: flex;
		gap: 20rpx;
}
.action-btn.data-v-8e60890c {
		flex: 1;
		padding: 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		border: none;
}
.action-btn.secondary.data-v-8e60890c {
		background: #f5f7fa;
		color: #666;
}
.action-btn.primary.data-v-8e60890c {
		background: #00b38a;
		color: white;
}
.action-btn.data-v-8e60890c:disabled {
		opacity: 0.6;
}

	/* 分析结果 */
.analysis-result.data-v-8e60890c {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
}
.result-header.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.result-title.data-v-8e60890c {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
}

	/* 图像识别结果 */
.recognition-section.data-v-8e60890c {
		background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 30rpx;
		border-left: 6rpx solid #4a90e2;
}
.recognition-header.data-v-8e60890c {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
}
.recognition-icon.data-v-8e60890c {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
}
.recognition-title.data-v-8e60890c {
		font-size: 28rpx;
		font-weight: 600;
		color: #4a90e2;
}
.recognition-content.data-v-8e60890c {
		background: rgba(255, 255, 255, 0.8);
		border-radius: 8rpx;
		padding: 20rpx;
}
.recognition-text.data-v-8e60890c {
		font-size: 26rpx;
		line-height: 1.6;
		color: #555;
		word-break: break-all;
}

	/* 食物信息 */
.food-info.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.food-name.data-v-8e60890c {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
}
.name-text.data-v-8e60890c {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.confidence-badge.data-v-8e60890c {
		background: #00b38a;
		color: white;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
}
.confidence-text.data-v-8e60890c {
		font-size: 24rpx;
}

	/* 营养成分 */
.nutrition-section.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.section-title.data-v-8e60890c {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
}
.nutrition-grid.data-v-8e60890c {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
}
.nutrition-item.data-v-8e60890c {
		text-align: center;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
}
.nutrition-label.data-v-8e60890c {
		display: block;
		font-size: 24rpx;
		color: #999;
		margin-bottom: 8rpx;
}
.nutrition-value.data-v-8e60890c {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #00b38a;
		margin-bottom: 4rpx;
}
.nutrition-unit.data-v-8e60890c {
		display: block;
		font-size: 20rpx;
		color: #999;
}

	/* GI值分析 */
.gi-section.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.gi-header.data-v-8e60890c {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
}
.gi-title.data-v-8e60890c {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.gi-badge.data-v-8e60890c {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		color: white;
}
.gi-badge.low.data-v-8e60890c {
		background: #4CAF50;
}
.gi-badge.medium.data-v-8e60890c {
		background: #ff9800;
}
.gi-badge.high.data-v-8e60890c {
		background: #f44336;
}
.gi-value.data-v-8e60890c {
		font-size: 24rpx;
		font-weight: bold;
}
.gi-description.data-v-8e60890c {
		padding: 20rpx;
		background: #f0f8ff;
		border-radius: 12rpx;
		border-left: 6rpx solid #00b38a;
}
.gi-text.data-v-8e60890c {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
}

	/* 健康建议 */
.advice-section.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.advice-header.data-v-8e60890c {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 20rpx;
}
.advice-icon.data-v-8e60890c {
		width: 32rpx;
		height: 32rpx;
}
.advice-title.data-v-8e60890c {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.advice-content.data-v-8e60890c {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
}
.advice-text.data-v-8e60890c {
		padding: 20rpx;
		background: #fff3cd;
		border-radius: 12rpx;
		border-left: 6rpx solid #ffc107;
		font-size: 28rpx;
		color: #856404;
		line-height: 1.6;
}
.advice-content-unified.data-v-8e60890c {
		padding: 24rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
		border-left: 6rpx solid #00b38a;
}
.advice-text-unified.data-v-8e60890c {
		font-size: 28rpx;
		color: #555;
		line-height: 1.8;
		text-align: justify;
}

	/* 详细分析 */
.detailed-analysis.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.analysis-section.data-v-8e60890c {
		margin-bottom: 24rpx;
}
.section-header.data-v-8e60890c {
		padding: 16rpx 20rpx;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
}
.section-header.can-eat.data-v-8e60890c {
		background: rgba(76, 175, 80, 0.1);
		border-left: 6rpx solid #4CAF50;
}
.section-header.limit-eat.data-v-8e60890c {
		background: rgba(255, 152, 0, 0.1);
		border-left: 6rpx solid #ff9800;
}
.section-header.suggestions.data-v-8e60890c {
		background: rgba(0, 179, 138, 0.1);
		border-left: 6rpx solid #00b38a;
}
.section-title.data-v-8e60890c {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
}
.food-list.data-v-8e60890c {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
}
.food-item.data-v-8e60890c {
		padding: 16rpx 20rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		border-left: 4rpx solid #e0e0e0;
}
.food-text.data-v-8e60890c {
		font-size: 28rpx;
		color: #555;
		line-height: 1.5;
}
.unified-content.data-v-8e60890c {
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		margin-top: 12rpx;
}
.unified-text.data-v-8e60890c {
		font-size: 28rpx;
		color: #555;
		line-height: 1.6;
		text-align: justify;
}

	/* 保存按钮 */
.save-section.data-v-8e60890c {
		margin-bottom: 30rpx;
}
.save-btn.data-v-8e60890c {
		width: 100%;
		padding: 24rpx;
		background: #00b38a;
		color: white;
		border: none;
		border-radius: 12rpx;
		font-size: 28rpx;
}

	/* 历史记录 */
.history-section.data-v-8e60890c {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
}
.history-header.data-v-8e60890c {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.history-title.data-v-8e60890c {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.view-all.data-v-8e60890c {
		font-size: 28rpx;
		color: #00b38a;
}
.history-list.data-v-8e60890c {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
}
.history-item.data-v-8e60890c {
		display: flex;
		align-items: center;
		gap: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
}
.history-image.data-v-8e60890c {
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
}
.history-info.data-v-8e60890c {
		flex: 1;
}
.history-food-name.data-v-8e60890c {
		display: block;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
}
.history-date.data-v-8e60890c {
		display: block;
		font-size: 24rpx;
		color: #999;
}
.history-gi.data-v-8e60890c {
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		color: white;
}
.history-gi.low.data-v-8e60890c {
		background: #4CAF50;
}
.history-gi.medium.data-v-8e60890c {
		background: #ff9800;
}
.history-gi.high.data-v-8e60890c {
		background: #f44336;
}
.history-gi-text.data-v-8e60890c {
		font-size: 20rpx;
}

	/* 分析状态样式 */
.analysis-status.data-v-8e60890c {
		margin-top: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
}
.status-item.data-v-8e60890c {
		display: flex;
		align-items: center;
		gap: 12rpx;
}
.loading-icon.data-v-8e60890c {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #e0e0e0;
		border-top: 3rpx solid #3b82f6;
		border-radius: 50%;
		animation: spin-8e60890c 1s linear infinite;
}
@keyframes spin-8e60890c {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.status-text.data-v-8e60890c {
		font-size: 28rpx;
		color: #3b82f6;
}
.error-item.data-v-8e60890c {
		display: flex;
		align-items: center;
		gap: 12rpx;
		margin-top: 12rpx;
}
.error-icon.data-v-8e60890c {
		font-size: 32rpx;
}
.error-text.data-v-8e60890c {
		font-size: 28rpx;
		color: #f44336;
		flex: 1;
}

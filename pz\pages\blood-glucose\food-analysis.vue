<template>
	<view class="page-container">
		<navbar :isHomePage="false" title="饮食分析" />

		<!-- 主体内容 -->
		<view class="content-container" :style="contentStyle">

			<!-- 上传区域 -->
			<view class="upload-section">
				<view class="upload-header">
					<text class="upload-title">AI食物识别</text>
					<text class="upload-subtitle">拍照或选择图片，AI将分析食物营养成分</text>
				</view>


				<view class="upload-area" @tap="chooseImage">
					<image v-if="selectedImage" :src="selectedImage" class="preview-image" mode="aspectFit"></image>
					<view v-else class="upload-placeholder">
						<image src="../../static/resource/images/camera.png" class="upload-icon"></image>
						<text class="upload-text">点击上传食物图片</text>
						<text class="upload-hint">支持JPG、PNG格式</text>
					</view>
				</view>

				<view class="upload-actions" v-if="selectedImage">
					<button class="action-btn secondary" @tap="reSelectImage">重新选择</button>
					<button class="action-btn primary" @tap="analyzeFood" :disabled="analyzing">
						{{ analyzing ? '分析中...' : '开始分析' }}
					</button>
				</view>

				<!-- 分析状态提示 -->
				<view class="analysis-status" v-if="analyzing || errorMessage">
					<view class="status-item" v-if="analyzing">
						<view class="loading-icon"></view>
						<text class="status-text">{{ currentStep }}</text>
					</view>
					<view class="error-item" v-if="errorMessage">
						<text class="error-icon">⚠️</text>
						<text class="error-text">{{ errorMessage }}</text>
					</view>
				</view>
			</view>

			<!-- 分析结果 -->
			<view class="analysis-result" v-if="analysisResult">
				<view class="result-header">
					<text class="result-title">分析结果</text>
				</view>

				<!-- 图像识别结果 -->
				<view class="recognition-section" v-if="qwenRecognitionResult">
					<view class="recognition-header">
						<image src="../../static/resource/images/血糖管理.png" class="recognition-icon"></image>
						<text class="recognition-title">AI图像识别结果</text>
					</view>
					<text class="name-text">{{ analysisResult.foodName }}</text>
				</view>

			

				<!-- 营养成分 -->
				<view class="nutrition-section">
					<view class="section-title">营养成分 (每100g)</view>
					<view class="nutrition-grid">
						<view class="nutrition-item">
							<text class="nutrition-label">热量</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.calories }}</text>
							<text class="nutrition-unit">kcal</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">碳水化合物</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.carbs }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">蛋白质</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.protein }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">脂肪</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.fat }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">膳食纤维</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.fiber }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">糖分</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.sugar }}</text>
							<text class="nutrition-unit">g</text>
						</view>
					</view>
				</view>


				<!-- 详细分析 -->
				<view class="detailed-analysis" v-if="analysisResult.detailedAnalysis">

					<!-- 健康建议 -->
					<view class="advice-section">
						<view class="advice-header">
							<image src="../../static/resource/images/tips.png" class="advice-icon"></image>
							<text class="advice-title">健康建议</text>
						</view>
						<view class="advice-content-unified">
							<text class="advice-text-unified">
								{{ analysisResult.healthAdvice.join('；') }}
							</text>
						</view>
					</view>
					<!-- 推荐食用 -->
					<view class="analysis-section">
						<view class="section-header can-eat">
							<text class="section-title">✅ 推荐食用</text>
						</view>
						<view class="unified-content">
							<text class="unified-text">{{ analysisResult.detailedAnalysis.canEat.join('；') }}</text>
						</view>
					</view>

					<!-- 需要控制 -->
					<view class="analysis-section">
						<view class="section-header limit-eat">
							<text class="section-title">⚠️ 需要控制</text>
						</view>
						<view class="unified-content">
							<text class="unified-text">{{ analysisResult.detailedAnalysis.limitEat.join('；') }}</text>
						</view>
					</view>

					<!-- 改进建议 -->
					<view class="analysis-section">
						<view class="section-header suggestions">
							<text class="section-title">💡 改进建议</text>
						</view>
						<view class="unified-content">
							<text
								class="unified-text">{{ analysisResult.detailedAnalysis.suggestions.join('；') }}</text>
						</view>
					</view>
				</view>


				<!-- 保存记录 -->
				<view class="save-section">
					<button class="save-btn" @tap="saveAnalysisRecord">保存到饮食记录</button>
				</view>
			</view>

			<!-- 历史记录 -->
			<view class="history-section" v-if="historyRecords.length > 0">
				<view class="history-header">
					<text class="history-title">最近分析</text>
					<text class="view-all" @tap="viewAllHistory">查看全部</text>
				</view>

				<view class="history-list">
					<view class="history-item" v-for="record in historyRecords.slice(0, 3)" :key="record.id"
						@tap="viewHistoryDetail(record)">
						<image :src="record.image" class="history-image" mode="aspectFill"></image>
						<view class="history-info">
							<text class="history-food-name">{{ record.foodName }}</text>
							<text class="history-date">{{ formatDate(record.timestamp) }}</text>
						</view>
						<view class="history-gi" :class="getGIClass(record.giValue)">
							<text class="history-gi-text">GI: {{ record.giValue }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'
	import navbar from '../../components/navbar/navbar.vue'
	import {
		sendMessageToAI
	} from '../../utils/ai/service'
	import {
		analyzeImageWithQwenVL,
		debugQwenVLResponse,
		cleanAndSimplifyDescription
	} from '../../utils/ai/qwenVLService'




	// 响应式数据
	const selectedImage = ref('')
	const analyzing = ref(false)
	const analysisResult = ref(null)
	const historyRecords = ref([])
	const currentStep = ref('') // 当前处理步骤
	const errorMessage = ref('') // 错误信息
	const qwenRecognitionResult = ref('') // 通义千问识别结果

	// 计算属性
	const contentStyle = computed(() => {
		const systemInfo = uni.getSystemInfoSync()
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
		const navHeight = menuButtonInfo.top + menuButtonInfo.height + 8
		return {
			paddingTop: navHeight + 'px'
		}
	})

	// 方法
	const chooseImage = () => {
		uni.chooseImage({
			count: 1,
			sizeType: ['compressed'],
			sourceType: ['camera', 'album'],
			success: (res) => {
				selectedImage.value = res.tempFilePaths[0]
				analysisResult.value = null
				console.log('图片选择成功:', res.tempFilePaths[0])
			},
			fail: (err) => {
				console.error('选择图片失败:', err)
				uni.showToast({
					title: '选择图片失败',
					icon: 'none'
				})
			}
		})
	}

	const reSelectImage = () => {
		selectedImage.value = ''
		analysisResult.value = null
	}

	/**
	 * AI分析食物营养成分
	 */
	const analyzeFood = async () => {
		if (!selectedImage.value) {
			uni.showToast({
				title: '请先选择图片',
				icon: 'none'
			})
			return
		}

		analyzing.value = true

		try {
			errorMessage.value = ''
			analysisResult.value = null // 清空之前的分析结果
			qwenRecognitionResult.value = '' // 清空之前的识别结果

			// 图片质量预检查
			currentStep.value = '正在检查图片质量...'
			console.log('开始图片质量检查...')

			// 检查图片文件大小（简单的质量指标）
			try {
				const fileInfo = await new Promise((resolve, reject) => {
					uni.getFileInfo({
						filePath: selectedImage.value,
						success: resolve,
						fail: reject
					})
				})

				console.log('图片文件信息:', fileInfo)

				// 检查文件大小（太小可能质量不佳，太大可能处理困难）
				if (fileInfo.size < 10000) { // 小于10KB
					console.warn('图片文件过小，可能质量不佳')
					uni.showToast({
						title: '图片质量可能不佳，建议重新拍摄',
						icon: 'none',
						duration: 2000
					})
				} else if (fileInfo.size > 10000000) { // 大于10MB
					console.warn('图片文件过大')
					uni.showToast({
						title: '图片文件较大，处理可能较慢',
						icon: 'none',
						duration: 2000
					})
				}
			} catch (fileError) {
				console.warn('🔍 [食物识别] 无法获取文件信息:', fileError)
			}

			// 第一步：使用通义千问VL-MAX进行图像识别
			currentStep.value = '正在识别食物...'
			console.log('🔍 [食物识别] ===== 第一步：通义千问VL-MAX图像识别 =====')
			uni.showLoading({
				title: '正在识别食物...',
				mask: true
			})

			// 先调试API响应结构
			console.log('🔍 [食物识别] ===== 调试API响应结构 =====')
			try {
				const debugResponse = await debugQwenVLResponse(selectedImage.value)
				console.log('🔍 [食物识别] 调试响应完成')
			} catch (debugError) {
				console.error('🔍 [食物识别] 调试响应失败:', debugError)
			}

			const qwenResult = await analyzeImageWithQwenVL(selectedImage.value)
			console.log('🔍 [食物识别] 通义千问VL-MAX原始返回结果:')
			console.log(qwenResult)
			console.log('🔍 [食物识别] 返回结果类型:', typeof qwenResult)

			// 处理不同类型的返回结果
			let qwenDescription = ''
			let userFriendlyDescription = ''

			if (qwenResult && typeof qwenResult === 'object' && qwenResult.rawDescription) {
				// 新格式：包含原始描述和用户友好描述
				qwenDescription = qwenResult.rawDescription
				userFriendlyDescription = qwenResult.userDescription
				console.log('🔍 [食物识别] 使用新格式 - 原始描述长度:', qwenDescription.length)
				console.log('🔍 [食物识别] 使用新格式 - 用户友好描述:', userFriendlyDescription)
			} else if (typeof qwenResult === 'string') {
				// 旧格式：直接是字符串
				qwenDescription = qwenResult
				userFriendlyDescription = cleanAndSimplifyDescription(qwenResult)
				console.log('🔍 [食物识别] 使用旧格式，进行文本清理')
			} else if (qwenResult && typeof qwenResult === 'object') {
				// 对象格式：尝试提取文本内容
				qwenDescription = qwenResult.content || qwenResult.text || qwenResult.message || JSON.stringify(qwenResult)
				userFriendlyDescription = cleanAndSimplifyDescription(qwenDescription)
				console.log('🔍 [食物识别] 从对象提取文本并清理')
			} else {
				qwenDescription = String(qwenResult || '')
				userFriendlyDescription = '识别到食物图片，正在进行营养分析。'
				console.log('🔍 [食物识别] 使用默认描述')
			}

			console.log('🔍 [食物识别] 最终原始描述长度:', qwenDescription.length)
			console.log('🔍 [食物识别] 最终用户友好描述:', userFriendlyDescription)
			console.log('🔍 [食物识别] ===== 通义千问VL-MAX识别完成 =====')

			// 验证识别结果是否有效
			if (!qwenDescription || qwenDescription.trim().length === 0) {
				throw new Error('通义千问VL-MAX识别失败：返回空结果')
			}

			// 保存识别结果到响应式变量，供前端显示（使用用户友好的版本）
			qwenRecognitionResult.value = userFriendlyDescription

			// 第二步：将通义千问的描述发送给DeepSeek进行结构化处理
			currentStep.value = '正在分析营养成分...'
			console.log('🔍 [食物识别] ===== 第二步：DeepSeek结构化处理 =====')
			uni.showLoading({
				title: '正在分析营养成分...',
				mask: true
			})

			// 构建更详细的prompt，确保DeepSeek理解任务
			const deepSeekPrompt = `你是一位专业的营养师和糖尿病饮食专家。请仔细分析以下通义千问VL-MAX提供的详细食物图像描述，并基于这个具体的食物描述进行营养分析。

=== 食物图像识别结果 ===
${qwenDescription}
=== 识别结果结束 ===

请基于上述具体的食物识别结果，进行专业的营养成分分析和糖尿病饮食建议。请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "根据识别结果确定的主要食物名称",
  "confidence": "根据识别清晰度确定的置信度数值(0-100)",
  "giValue": "根据具体食物确定的血糖生成指数(0-100)",
  "nutrition": {
    "calories": "根据食物类型和份量确定的卡路里数值",
    "carbs": "根据食物确定的碳水化合物含量(克)",
    "protein": "根据食物确定的蛋白质含量(克)",
    "fat": "根据食物确定的脂肪含量(克)",
    "fiber": "根据食物确定的纤维含量(克)",
    "sugar": "根据食物确定的糖分含量(克)"
  },
  "healthAdvice": [
    "基于具体食物特点的健康建议1",
    "基于具体食物特点的健康建议2",
    "基于具体食物特点的健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "基于识别食物推荐食用的内容及原因"
    ],
    "limitEat": [
      "基于识别食物需要限制的内容及原因"
    ],
    "suggestions": [
      "基于具体食物的个性化饮食建议"
    ]
  }
}

分析要求：
1. 必须基于上述具体的食物识别结果进行分析，不要使用通用数据
2. 所有数值必须是合理的营养数据，符合实际食物特征
3. GI值范围在0-100之间，要准确反映食物的血糖影响
4. 健康建议要针对糖尿病患者，结合具体食物特点
5. foodName要与识别结果中的主要食物一致
6. 分析要专业且实用，避免泛泛而谈
7. 返回纯JSON格式，不要markdown代码块或其他格式`

			console.log('🔍 [食物识别] ===== 数据传递验证 =====')
			console.log('🔍 [食物识别] qwenDescription变量值:')
			console.log(qwenDescription)
			console.log('🔍 [食物识别] prompt中是否包含识别结果:', deepSeekPrompt.includes(qwenDescription))
			console.log('🔍 [食物识别] 完整的DeepSeek请求内容:')
			console.log(deepSeekPrompt)
			console.log('🔍 [食物识别] ===== 发送请求到DeepSeek =====')

			const deepSeekResponse = await sendMessageToAI(deepSeekPrompt)
			console.log('🔍 [食物识别] ===== DeepSeek响应接收 =====')
			console.log('🔍 [食物识别] DeepSeek返回的原始结果:')
			console.log(deepSeekResponse)
			console.log('🔍 [食物识别] 响应类型:', typeof deepSeekResponse)
			console.log('🔍 [食物识别] 响应长度:', deepSeekResponse ? deepSeekResponse.length : 0)
			console.log('🔍 [食物识别] ===== DeepSeek处理完成 =====')

			// 第三步：解析DeepSeek返回的JSON结果
			console.log('🔍 [食物识别] ===== 第三步：JSON解析处理 =====')
			let parsedResult
			try {
				// 清理可能的markdown格式
				const cleanedResponse = deepSeekResponse.replace(/```json\s*|\s*```/g, '').trim()
				console.log('🔍 [食物识别] 清理后的响应内容:')
				console.log(cleanedResponse)
				console.log('🔍 [食物识别] 开始JSON解析...')

				parsedResult = JSON.parse(cleanedResponse)
				console.log('🔍 [食物识别] ===== JSON解析成功 =====')
				console.log('🔍 [食物识别] 解析后的结果:')
				console.log(parsedResult)

				// 验证解析结果的关键字段
				console.log('🔍 [食物识别] 结果验证:')
				console.log('🔍 [食物识别] - foodName:', parsedResult.foodName)
				console.log('🔍 [食物识别] - confidence:', parsedResult.confidence)
				console.log('🔍 [食物识别] - giValue:', parsedResult.giValue)
				console.log('🔍 [食物识别] - nutrition:', parsedResult.nutrition)

			} catch (parseError) {
				console.error('🔍 [食物识别] ===== JSON解析失败 =====')
				console.error('🔍 [食物识别] 解析错误:', parseError)
				console.error('🔍 [食物识别] 错误详情:', parseError.message)
				console.error('🔍 [食物识别] 原始响应内容:', deepSeekResponse)
				console.error('🔍 [食物识别] 清理后内容:', deepSeekResponse.replace(/```json\s*|\s*```/g, '').trim())
				// 使用备用数据结构，尝试基于通义千问的识别结果
				console.log('🔍 [食物识别] 生成备用数据结构...')

				// 尝试从通义千问结果中提取食物名称
				let extractedFoodName = "识别的食物"
				if (qwenDescription && qwenDescription.length > 0) {
					// 简单的食物名称提取逻辑
					const foodKeywords = ['米饭', '面条', '苹果', '香蕉', '鸡蛋', '牛奶', '面包', '蔬菜', '肉类', '鱼类']
					for (const keyword of foodKeywords) {
						if (qwenDescription.includes(keyword)) {
							extractedFoodName = keyword
							break
						}
					}
				}

				parsedResult = {
					foodName: extractedFoodName,
					confidence: 75, // 降低置信度，因为是备用数据
					giValue: 60,
					nutrition: {
						calories: 150,
						carbs: 30,
						protein: 6,
						fat: 5,
						fiber: 3,
						sugar: 8
					},
					healthAdvice: [
						`基于图像识别，${extractedFoodName}需要注意控制食用份量`,
						"建议搭配低GI食物一起食用",
						"餐后适当运动有助于血糖控制"
					],
					detailedAnalysis: {
						canEat: [`根据识别结果，${extractedFoodName}适量食用有益健康`],
						limitEat: ["注意控制高糖高脂食物的摄入"],
						suggestions: ["由于AI分析服务暂时不可用，建议咨询专业营养师获取准确的饮食建议"]
					}
				}
				console.log('🔍 [食物识别] 备用数据结构生成完成:', parsedResult)
				console.log('🔍 [食物识别] 提取的食物名称:', extractedFoodName)
			}

			// 设置分析结果
			console.log('🔍 [食物识别] ===== 设置最终分析结果 =====')
			console.log('🔍 [食物识别] 最终结果数据:')
			console.log(parsedResult)

			// 验证结果的完整性
			const requiredFields = ['foodName', 'confidence', 'giValue', 'nutrition', 'healthAdvice', 'detailedAnalysis']
			const missingFields = requiredFields.filter(field => !parsedResult[field])
			if (missingFields.length > 0) {
				console.warn('🔍 [食物识别] 警告：结果缺少必要字段:', missingFields)
			}

			analysisResult.value = parsedResult
			console.log('🔍 [食物识别] 分析结果已设置到响应式变量')
			console.log('🔍 [食物识别] analysisResult.value:', analysisResult.value)

			// 保存到历史记录
			const newRecord = {
				id: Date.now(),
				image: selectedImage.value,
				foodName: analysisResult.value.foodName,
				giValue: analysisResult.value.giValue,
				timestamp: Date.now(),
				confidence: analysisResult.value.confidence,
				nutrition: analysisResult.value.nutrition,
				healthAdvice: analysisResult.value.healthAdvice,
				detailedAnalysis: analysisResult.value.detailedAnalysis,
				date: new Date().toLocaleDateString('zh-CN', {
					month: 'numeric',
					day: 'numeric',
					hour: '2-digit',
					minute: '2-digit'
				})
			}
			saveHistoryRecord(newRecord)

			uni.hideLoading()
			uni.showToast({
				title: '分析完成',
				icon: 'success'
			})

		} catch (error) {
			console.error('🔍 [食物识别] 分析失败:', error)
			console.error('🔍 [食物识别] 错误详情:', {
				message: error.message,
				stack: error.stack,
				name: error.name
			})
			uni.hideLoading()

			// 根据错误类型提供不同的提示
			let errorTitle = '分析失败，请重试'
			if (error.message && error.message.includes('网络')) {
				errorTitle = '网络连接失败，请检查网络'
				errorMessage.value = '请检查网络连接后重试'
				console.error('🔍 [食物识别] 网络错误:', error.message)
			} else if (error.message && error.message.includes('通义千问')) {
				errorTitle = '图片识别失败'
				errorMessage.value = '请选择清晰的食物图片重试'
				console.error('🔍 [食物识别] 通义千问VL-MAX错误:', error.message)
			} else if (error.message && error.message.includes('AI服务')) {
				errorTitle = 'AI分析服务暂时不可用'
				errorMessage.value = '服务暂时繁忙，请稍后重试'
				console.error('🔍 [食物识别] DeepSeek API错误:', error.message)
			} else {
				errorMessage.value = '分析过程中出现错误，请重新尝试'
				console.error('🔍 [食物识别] 未知错误:', error.message)
			}

			uni.showToast({
				title: errorTitle,
				icon: 'none',
				duration: 3000
			})

			// 清空识别结果，避免显示错误的内容
			qwenRecognitionResult.value = ''
			analysisResult.value = null
		} finally {
			analyzing.value = false
			currentStep.value = ''
		}
	}

	// 获取GI值等级样式类
	const getGIClass = (giValue) => {
		if (giValue <= 55) return 'low'
		if (giValue <= 70) return 'medium'
		return 'high'
	}

	// 获取GI值描述
	const getGIDescription = (giValue) => {
		if (giValue <= 55) return '低GI食物，对血糖影响较小，适合糖尿病患者食用'
		if (giValue <= 70) return '中等GI食物，适量食用，建议搭配低GI食物'
		return '高GI食物，会快速升高血糖，糖尿病患者需严格控制'
	}

	// 备用分析结果生成
	const generateBackupAnalysis = (foodName) => {
		const foodDatabase = {
			'白米饭': {
				foodName: '白米饭',
				confidence: 95,
				giValue: 83,
				nutrition: {
					calories: 130,
					carbs: 28,
					protein: 2.7,
					fat: 0.3,
					fiber: 0.4,
					sugar: 0.1
				},
				healthAdvice: ['属于高GI食物，会快速升高血糖', '建议搭配蔬菜和蛋白质一起食用', '可以选择糙米或杂粮饭作为替代']
			},
			'苹果': {
				foodName: '苹果',
				confidence: 92,
				giValue: 36,
				nutrition: {
					calories: 52,
					carbs: 14,
					protein: 0.3,
					fat: 0.2,
					fiber: 2.4,
					sugar: 10
				},
				healthAdvice: ['属于低GI食物，对血糖影响较小', '富含膳食纤维，有助于血糖稳定', '建议在两餐之间食用，避免空腹']
			},
			'糙米饭': {
				foodName: '糙米饭',
				confidence: 90,
				giValue: 50,
				nutrition: {
					calories: 112,
					carbs: 23,
					protein: 2.6,
					fat: 0.9,
					fiber: 1.8,
					sugar: 0.4
				},
				healthAdvice: ['中等GI食物，比白米饭更适合糖尿病患者', '富含膳食纤维，有助于血糖稳定', '建议控制食用量，搭配蔬菜食用']
			}
		}

		return foodDatabase[foodName] || {
			foodName: foodName,
			confidence: 85,
			giValue: 55,
			nutrition: {
				calories: 100,
				carbs: 20,
				protein: 3,
				fat: 1,
				fiber: 2,
				sugar: 5
			},
			healthAdvice: ['请咨询营养师获取准确信息', '建议适量食用', '注意监测血糖变化']
		}
	}



	const saveAnalysisRecord = () => {
		if (!analysisResult.value) return

		const record = {
			id: Date.now(),
			image: selectedImage.value,
			foodName: analysisResult.value.foodName,
			confidence: analysisResult.value.confidence,
			giValue: analysisResult.value.giValue,
			nutrition: analysisResult.value.nutrition,
			healthAdvice: analysisResult.value.healthAdvice,
			timestamp: new Date().toISOString()
		}

		const existingRecords = uni.getStorageSync('foodAnalysisRecords') || []
		existingRecords.unshift(record)
		uni.setStorageSync('foodAnalysisRecords', existingRecords)

		historyRecords.value = existingRecords

		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})
	}

	const loadHistoryRecords = () => {
		const records = uni.getStorageSync('foodAnalysisRecords') || []
		historyRecords.value = records
	}

	const saveHistoryRecord = (record) => {
		try {
			const records = uni.getStorageSync('foodAnalysisRecords') || []
			records.unshift(record)
			// 只保留最近50条记录
			if (records.length > 50) {
				records.splice(50)
			}
			uni.setStorageSync('foodAnalysisRecords', records)
			historyRecords.value = records
		} catch (error) {
			console.error('保存历史记录失败:', error)
		}
	}

	const formatDate = (timestamp) => {
		const date = new Date(timestamp)
		const month = date.getMonth() + 1
		const day = date.getDate()
		const hours = date.getHours().toString().padStart(2, '0')
		const minutes = date.getMinutes().toString().padStart(2, '0')
		return `${month}月${day}日 ${hours}:${minutes}`
	}

	const viewHistoryDetail = (record) => {
		// 显示历史记录详情
		analysisResult.value = record
		selectedImage.value = record.image
	}

	const viewAllHistory = () => {
		uni.navigateTo({
			url: '/pages/blood-glucose/food-history'
		})
	}

	onMounted(() => {
		loadHistoryRecords()
	})
</script>

<style scoped>
	.page-container {
		min-height: 100vh;
		background-color: #f5f7fa;
	}

	.content-container {
		padding: 20rpx;
	}

	/* 上传区域 */
	.upload-section {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.upload-header {
		text-align: center;
		margin-bottom: 30rpx;
	}

	.upload-title {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 12rpx;
	}

	.upload-subtitle {
		display: block;
		font-size: 28rpx;
		color: #999;
	}

	.upload-area {
		border: 3rpx dashed #e0e0e0;
		border-radius: 16rpx;
		min-height: 400rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		transition: all 0.3s;
	}

	.upload-area:active {
		border-color: #00b38a;
		background: rgba(0, 179, 138, 0.05);
	}

	.preview-image {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
	}

	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
	}

	.upload-icon {
		width: 80rpx;
		height: 80rpx;
		opacity: 0.6;
	}

	.upload-text {
		font-size: 32rpx;
		color: #666;
	}

	.upload-hint {
		font-size: 24rpx;
		color: #999;
	}

	.upload-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		flex: 1;
		padding: 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		border: none;
	}

	.action-btn.secondary {
		background: #f5f7fa;
		color: #666;
	}

	.action-btn.primary {
		background: #00b38a;
		color: white;
	}

	.action-btn:disabled {
		opacity: 0.6;
	}

	/* 分析结果 */
	.analysis-result {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.result-header {
		margin-bottom: 30rpx;
	}

	.result-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	/* 图像识别结果 */
	.recognition-section {
		background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 30rpx;
		border-left: 6rpx solid #4a90e2;
	}

	.recognition-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.recognition-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}

	.recognition-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #4a90e2;
	}

	.recognition-content {
		background: rgba(255, 255, 255, 0.8);
		border-radius: 8rpx;
		padding: 20rpx;
	}

	.recognition-text {
		font-size: 26rpx;
		line-height: 1.6;
		color: #555;
		word-break: break-all;
	}

	/* 食物信息 */
	.food-info {
		margin-bottom: 30rpx;
	}

	.food-name {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
	}

	.name-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.confidence-badge {
		background: #00b38a;
		color: white;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	.confidence-text {
		font-size: 24rpx;
	}

	/* 营养成分 */
	.nutrition-section {
		margin-bottom: 30rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.nutrition-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
	}

	.nutrition-item {
		text-align: center;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
	}

	.nutrition-label {
		display: block;
		font-size: 24rpx;
		color: #999;
		margin-bottom: 8rpx;
	}

	.nutrition-value {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #00b38a;
		margin-bottom: 4rpx;
	}

	.nutrition-unit {
		display: block;
		font-size: 20rpx;
		color: #999;
	}

	/* GI值分析 */
	.gi-section {
		margin-bottom: 30rpx;
	}

	.gi-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}

	.gi-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.gi-badge {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		color: white;
	}

	.gi-badge.low {
		background: #4CAF50;
	}

	.gi-badge.medium {
		background: #ff9800;
	}

	.gi-badge.high {
		background: #f44336;
	}

	.gi-value {
		font-size: 24rpx;
		font-weight: bold;
	}

	.gi-description {
		padding: 20rpx;
		background: #f0f8ff;
		border-radius: 12rpx;
		border-left: 6rpx solid #00b38a;
	}

	.gi-text {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}

	/* 健康建议 */
	.advice-section {
		margin-bottom: 30rpx;
	}

	.advice-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 20rpx;
	}

	.advice-icon {
		width: 32rpx;
		height: 32rpx;
	}

	.advice-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.advice-content {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.advice-text {
		padding: 20rpx;
		background: #fff3cd;
		border-radius: 12rpx;
		border-left: 6rpx solid #ffc107;
		font-size: 28rpx;
		color: #856404;
		line-height: 1.6;
	}

	.advice-content-unified {
		padding: 24rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
		border-left: 6rpx solid #00b38a;
	}

	.advice-text-unified {
		font-size: 28rpx;
		color: #555;
		line-height: 1.8;
		text-align: justify;
	}

	/* 详细分析 */
	.detailed-analysis {
		margin-bottom: 30rpx;
	}

	.analysis-section {
		margin-bottom: 24rpx;
	}

	.section-header {
		padding: 16rpx 20rpx;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
	}

	.section-header.can-eat {
		background: rgba(76, 175, 80, 0.1);
		border-left: 6rpx solid #4CAF50;
	}

	.section-header.limit-eat {
		background: rgba(255, 152, 0, 0.1);
		border-left: 6rpx solid #ff9800;
	}

	.section-header.suggestions {
		background: rgba(0, 179, 138, 0.1);
		border-left: 6rpx solid #00b38a;
	}

	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}

	.food-list {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.food-item {
		padding: 16rpx 20rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		border-left: 4rpx solid #e0e0e0;
	}

	.food-text {
		font-size: 28rpx;
		color: #555;
		line-height: 1.5;
	}

	.unified-content {
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		margin-top: 12rpx;
	}

	.unified-text {
		font-size: 28rpx;
		color: #555;
		line-height: 1.6;
		text-align: justify;
	}

	/* 保存按钮 */
	.save-section {
		margin-bottom: 30rpx;
	}

	.save-btn {
		width: 100%;
		padding: 24rpx;
		background: #00b38a;
		color: white;
		border: none;
		border-radius: 12rpx;
		font-size: 28rpx;
	}

	/* 历史记录 */
	.history-section {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
	}

	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.history-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.view-all {
		font-size: 28rpx;
		color: #00b38a;
	}

	.history-list {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.history-item {
		display: flex;
		align-items: center;
		gap: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
	}

	.history-image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
	}

	.history-info {
		flex: 1;
	}

	.history-food-name {
		display: block;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
	}

	.history-date {
		display: block;
		font-size: 24rpx;
		color: #999;
	}

	.history-gi {
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		color: white;
	}

	.history-gi.low {
		background: #4CAF50;
	}

	.history-gi.medium {
		background: #ff9800;
	}

	.history-gi.high {
		background: #f44336;
	}

	.history-gi-text {
		font-size: 20rpx;
	}

	/* 分析状态样式 */
	.analysis-status {
		margin-top: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
	}

	.status-item {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.loading-icon {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #e0e0e0;
		border-top: 3rpx solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.status-text {
		font-size: 28rpx;
		color: #3b82f6;
	}

	.error-item {
		display: flex;
		align-items: center;
		gap: 12rpx;
		margin-top: 12rpx;
	}

	.error-icon {
		font-size: 32rpx;
	}

	.error-text {
		font-size: 28rpx;
		color: #f44336;
		flex: 1;
	}
</style>
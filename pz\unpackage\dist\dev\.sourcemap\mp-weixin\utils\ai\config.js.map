{"version": 3, "file": "config.js", "sources": ["utils/ai/config.js"], "sourcesContent": ["// DeepSeek API配置\nexport const DEEPSEEK_BASE_URL = 'https://api.deepseek.com/v1/chat/completions';\nexport const DEEPSEEK_API_KEY = '***********************************';\n\n// 腾讯地图API配置\nexport const MAP_CONFIG = {\n  API_KEY: '4Q3BZ-45IKJ-HDVFH-DHRJ2-KHCBT-VTFHH',\n  GEOCODER_URL: 'https://apis.map.qq.com/ws/geocoder/v1/',\n  LOCATION_TYPES: {\n    GCJ02: 'gcj02'\n  },\n  TIMEOUT: 30000,\n  PARAMS: {\n    get_poi: 1,\n    output: 'json',\n    coord_type: 5\n  }\n};\n\n// 位置服务配置\nexport const LOCATION_CONFIG = {\n  TIMEOUT: 30000,\n  ERROR_MESSAGES: {\n    LOCATION_FAIL: '获取位置信息失败，请检查是否已授权位置权限',\n    GEOCODER_FAIL: '获取地址信息失败',\n    TIMEOUT: '获取位置信息超时'\n  }\n};\n\n// 通义千问VL-MAX配置\nexport const QWEN_VL_CONFIG = {\n  BASE_URL: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',\n  API_KEY: 'sk-948691927af4402a92cb566e1d8e153f',\n  MODEL: 'qwen-vl-max',\n  TIMEOUT: 60000,\n  MAX_RETRIES: 3\n};\n\n// AI服务配置\nexport const AI_CONFIG = {\n  TIMEOUT: 120000,\n  MAX_RETRIES: 3,\n  RETRY_DELAY: 2000,\n  ERROR_MESSAGES: {\n    REQUEST_FAIL: 'AI服务请求失败，请稍后重试',\n    TIMEOUT: 'AI服务响应超时，请稍后重试',\n    NETWORK_ERROR: '网络连接失败，请检查网络设置'\n  }\n};\n\n"], "names": [], "mappings": ";AACY,MAAC,oBAAoB;AACrB,MAAC,mBAAmB;AA4BpB,MAAC,iBAAiB;AAAA,EAC5B,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,aAAa;AACf;AAGY,MAAC,YAAY;AAAA,EACvB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,aAAa;AAAA,EACb,gBAAgB;AAAA,IACd,cAAc;AAAA,IACd,SAAS;AAAA,IACT,eAAe;AAAA,EAChB;AACH;;;;;"}
{"version": 3, "file": "qwenVLService.js", "sources": ["utils/ai/qwenVLService.js"], "sourcesContent": ["// 通义千问VL-MAX图像识别服务\nimport { QWEN_VL_CONFIG } from './config.js';\n\n/**\n * 将图片转换为base64格式\n * @param {string} filePath 图片文件路径\n * @returns {Promise<string>} base64编码的图片数据\n */\nfunction getBase64(filePath) {\n  return new Promise((resolve, reject) => {\n    uni.getFileSystemManager().readFile({\n      filePath: filePath,\n      encoding: 'base64',\n      success: (res) => {\n        resolve(res.data);\n      },\n      fail: (err) => {\n        console.error('读取图片文件失败:', err);\n        reject(new Error('读取图片文件失败'));\n      }\n    });\n  });\n}\n\n/**\n * 调用通义千问VL-MAX进行食物图像识别\n * @param {string} imagePath 图片路径\n * @returns {Promise<string>} 图像描述结果\n */\nexport async function analyzeImageWithQwenVL(imagePath) {\n  try {\n    console.log('🔍 [通义千问VL-MAX] 开始分析食物图片...');\n    console.log('🔍 [通义千问VL-MAX] 图片路径:', imagePath);\n\n    // 获取图片的base64编码\n    const base64Image = await getBase64(imagePath);\n    console.log('🔍 [通义千问VL-MAX] 图片base64编码获取成功，长度:', base64Image.length);\n\n    // 构建请求数据\n    const requestData = {\n      model: QWEN_VL_CONFIG.MODEL,\n      input: {\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                text: `请详细分析这张食物图片，包括以下信息：\n1. 食物种类和名称（尽可能具体）\n2. 食物的数量和大小\n3. 食物的外观特征（颜色、形状、质地等）\n4. 烹饪方式（如炒、煮、蒸、烤等）\n5. 配菜和搭配情况\n6. 食物的新鲜程度和品质\n7. 估计的份量大小\n8. 可能的营养特点\n\n请用中文详细描述，描述越详细越好，这将用于后续的营养分析。`\n              },\n              {\n                image: `data:image/jpeg;base64,${base64Image}`\n              }\n            ]\n          }\n        ]\n      },\n      parameters: {\n        result_format: \"message\"\n      }\n    };\n\n    console.log('🔍 [通义千问VL-MAX] 发送请求到API...');\n\n    // 发送请求到通义千问VL-MAX API\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.BASE_URL,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`\n      },\n      timeout: QWEN_VL_CONFIG.TIMEOUT\n    });\n\n    console.log('🔍 [通义千问VL-MAX] API响应状态:', response.statusCode);\n    console.log('🔍 [通义千问VL-MAX] API响应数据完整结构:', JSON.stringify(response.data, null, 2));\n\n    // 检查响应状态\n    if (response.statusCode !== 200) {\n      throw new Error(`通义千问VL-MAX API请求失败: HTTP ${response.statusCode}`);\n    }\n\n    // 解析响应数据\n    const responseData = response.data;\n    console.log('🔍 [通义千问VL-MAX] responseData:', responseData);\n    console.log('🔍 [通义千问VL-MAX] responseData.output:', responseData?.output);\n\n    if (!responseData || !responseData.output) {\n      throw new Error(`通义千问VL-MAX API返回错误: ${responseData?.message || '未知错误'}`);\n    }\n\n    // 详细检查数据结构\n    console.log('🔍 [通义千问VL-MAX] output.choices:', responseData.output?.choices);\n    console.log('🔍 [通义千问VL-MAX] choices[0]:', responseData.output?.choices?.[0]);\n    console.log('🔍 [通义千问VL-MAX] choices[0].message:', responseData.output?.choices?.[0]?.message);\n    console.log('🔍 [通义千问VL-MAX] choices[0].message.content:', responseData.output?.choices?.[0]?.message?.content);\n\n    // 提取图像描述结果\n    const imageDescription = responseData.output?.choices?.[0]?.message?.content;\n    console.log('🔍 [通义千问VL-MAX] 提取的imageDescription:', imageDescription);\n    console.log('🔍 [通义千问VL-MAX] imageDescription类型:', typeof imageDescription);\n\n    if (!imageDescription) {\n      // 尝试其他可能的路径\n      console.log('🔍 [通义千问VL-MAX] 尝试其他数据路径...');\n      console.log('🔍 [通义千问VL-MAX] responseData.output.text:', responseData.output?.text);\n      console.log('🔍 [通义千问VL-MAX] responseData.output.content:', responseData.output?.content);\n      console.log('🔍 [通义千问VL-MAX] responseData.choices:', responseData?.choices);\n\n      throw new Error('通义千问VL-MAX API返回的数据格式不正确');\n    }\n\n    console.log('🔍 [通义千问VL-MAX] 图像识别成功');\n    console.log('🔍 [通义千问VL-MAX] 识别结果类型:', typeof imageDescription);\n    console.log('🔍 [通义千问VL-MAX] 识别结果:', imageDescription);\n\n    // 处理不同类型的返回结果\n    let finalResult = '';\n\n    if (typeof imageDescription === 'string') {\n      finalResult = imageDescription;\n    } else if (imageDescription && typeof imageDescription === 'object') {\n      console.log('🔍 [通义千问VL-MAX] imageDescription是对象，尝试提取文本...');\n\n      // 尝试多种可能的字段\n      finalResult = imageDescription.text ||\n        imageDescription.content ||\n        imageDescription.message ||\n        imageDescription.value ||\n        imageDescription.result;\n\n      // 如果还是对象，继续深入提取\n      if (typeof finalResult === 'object' && finalResult) {\n        finalResult = finalResult.text ||\n          finalResult.content ||\n          finalResult.message ||\n          JSON.stringify(finalResult);\n      }\n\n      // 如果仍然没有找到文本，序列化整个对象\n      if (!finalResult || typeof finalResult !== 'string') {\n        console.log('🔍 [通义千问VL-MAX] 无法提取文本，使用JSON序列化');\n        finalResult = JSON.stringify(imageDescription);\n      }\n    } else {\n      finalResult = String(imageDescription || '');\n    }\n\n    console.log('🔍 [通义千问VL-MAX] 最终处理结果:', finalResult);\n    console.log('🔍 [通义千问VL-MAX] 最终结果类型:', typeof finalResult);\n    console.log('🔍 [通义千问VL-MAX] 最终结果长度:', finalResult.length);\n\n    // 清理和简化描述，生成用户友好的版本\n    const userFriendlyDescription = cleanAndSimplifyDescription(finalResult);\n    console.log('🔍 [通义千问VL-MAX] 用户友好描述:', userFriendlyDescription);\n\n    // 返回包含原始结果和用户友好描述的对象\n    return {\n      rawDescription: finalResult,           // 原始详细描述（用于DeepSeek分析）\n      userDescription: userFriendlyDescription  // 用户友好描述（用于界面显示）\n    };\n\n  } catch (error) {\n    console.error('🔍 [通义千问VL-MAX] 图像识别失败:', error);\n\n    // 根据错误类型提供不同的错误信息\n    if (error.message.includes('网络')) {\n      throw new Error('网络连接失败，请检查网络设置后重试');\n    } else if (error.message.includes('API')) {\n      throw new Error('图像识别服务暂时不可用，请稍后重试');\n    } else {\n      throw new Error(`图像识别失败: ${error.message}`);\n    }\n  }\n}\n\n/**\n * 清理和简化AI识别结果，转换为用户友好的描述\n * @param {string} rawDescription 原始AI识别结果\n * @returns {string} 清理后的用户友好描述\n */\nexport function cleanAndSimplifyDescription(rawDescription) {\n  if (!rawDescription || typeof rawDescription !== 'string') {\n    return '未能识别到具体食物信息';\n  }\n\n  try {\n    // 移除所有格式化字符和技术标记\n    let cleaned = rawDescription\n      .replace(/\\\\n/g, ' ')           // 移除换行符\n      .replace(/\\\\t/g, ' ')           // 移除制表符\n      .replace(/\\n/g, ' ')            // 移除实际换行\n      .replace(/\\t/g, ' ')            // 移除实际制表符\n      .replace(/###\\s*\\d+\\./g, '')    // 移除编号标记如 \"### 1.\"\n      .replace(/\\*\\*/g, '')           // 移除加粗标记\n      .replace(/\\*/g, '')             // 移除星号\n      .replace(/#{1,6}\\s*/g, '')      // 移除标题标记\n      .replace(/\\[.*?\\]/g, '')        // 移除方括号内容\n      .replace(/\\{.*?\\}/g, '')        // 移除花括号内容\n      .replace(/请详细分析.*?营养分析。/g, '') // 移除提示词\n      .replace(/包括以下信息.*?营养特点/g, '') // 移除指令部分\n      .replace(/\\d+\\.\\s*/g, '')       // 移除数字编号\n      .replace(/：\\s*/g, '：')        // 规范冒号格式\n      .replace(/\\s+/g, ' ')           // 合并多个空格\n      .trim();\n\n    // 提取核心食物信息\n    const foodPatterns = [\n      /(?:图片中显示|可以看到|识别到).*?([^。]+)/,\n      /主要.*?([^。]+)/,\n      /食物.*?([^。]+)/,\n      /([^。]*(?:米饭|面条|蔬菜|肉类|鱼类|水果|汤|粥|面包|蛋糕|饼干)[^。]*)/\n    ];\n\n    let extractedInfo = [];\n\n    // 尝试提取主要食物信息\n    for (const pattern of foodPatterns) {\n      const match = cleaned.match(pattern);\n      if (match && match[1]) {\n        const info = match[1].trim();\n        if (info.length > 5 && info.length < 100) {\n          extractedInfo.push(info);\n        }\n      }\n    }\n\n    // 如果没有提取到有效信息，尝试获取前100个字符\n    if (extractedInfo.length === 0) {\n      const firstSentence = cleaned.split(/[。！？]/)[0];\n      if (firstSentence && firstSentence.length > 10) {\n        extractedInfo.push(firstSentence);\n      }\n    }\n\n    // 生成最终的用户友好描述\n    if (extractedInfo.length > 0) {\n      let result = extractedInfo[0];\n\n      // 确保描述以合适的方式开始\n      if (!result.match(/^(识别到|图片中|可以看到)/)) {\n        result = '识别到' + result;\n      }\n\n      // 确保描述以句号结尾\n      if (!result.endsWith('。') && !result.endsWith('！') && !result.endsWith('？')) {\n        result += '。';\n      }\n\n      // 限制长度，保持简洁\n      if (result.length > 80) {\n        result = result.substring(0, 77) + '...';\n      }\n\n      return result;\n    }\n\n    return '识别到食物图片，正在进行营养分析。';\n\n  } catch (error) {\n    console.error('🔍 [文本清理] 处理失败:', error);\n    return '识别到食物图片，正在进行营养分析。';\n  }\n}\n\n/**\n * 调试通义千问VL-MAX API响应结构\n * @param {string} imagePath 图片路径\n * @returns {Promise<object>} 原始响应数据\n */\nexport async function debugQwenVLResponse(imagePath) {\n  try {\n    console.log('🔍 [调试] 开始调试通义千问VL-MAX API响应结构...');\n\n    const base64Image = await getBase64(imagePath);\n    const requestData = {\n      model: QWEN_VL_CONFIG.MODEL,\n      input: {\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                text: \"请简单描述这张图片中的食物。\"\n              },\n              {\n                image: `data:image/jpeg;base64,${base64Image}`\n              }\n            ]\n          }\n        ]\n      },\n      parameters: {\n        result_format: \"message\"\n      }\n    };\n\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.BASE_URL,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`\n      },\n      timeout: QWEN_VL_CONFIG.TIMEOUT\n    });\n\n    console.log('🔍 [调试] 完整的API响应:', JSON.stringify(response, null, 2));\n    return response;\n\n  } catch (error) {\n    console.error('🔍 [调试] 调试失败:', error);\n    throw error;\n  }\n}\n\n/**\n * 测试通义千问VL-MAX API连接\n * @returns {Promise<boolean>} 测试结果\n */\nexport async function testQwenVLConnection() {\n  try {\n    console.log('🔍 [通义千问VL-MAX] 测试API连接...');\n\n    // 创建一个简单的测试请求\n    const testData = {\n      model: QWEN_VL_CONFIG.MODEL,\n      input: {\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                text: \"测试连接\"\n              }\n            ]\n          }\n        ]\n      }\n    };\n\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.BASE_URL,\n      method: 'POST',\n      data: testData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`\n      },\n      timeout: 10000\n    });\n\n    console.log('🔍 [通义千问VL-MAX] 连接测试结果:', response.statusCode);\n    return response.statusCode === 200;\n\n  } catch (error) {\n    console.error('🔍 [通义千问VL-MAX] 连接测试失败:', error);\n    return false;\n  }\n}\n"], "names": ["uni", "QWEN_VL_CONFIG"], "mappings": ";;;AAQA,SAAS,UAAU,UAAU;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,wBAAI,qBAAsB,EAAC,SAAS;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,SAAS,CAAC,QAAQ;AAChB,gBAAQ,IAAI,IAAI;AAAA,MACjB;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,GAAG;AAC9B,eAAO,IAAI,MAAM,UAAU,CAAC;AAAA,MAC7B;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAOO,eAAe,uBAAuB,WAAW;;AACtD,MAAI;AACFA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AACzCA,kBAAY,MAAA,MAAA,OAAA,mCAAA,yBAAyB,SAAS;AAG9C,UAAM,cAAc,MAAM,UAAU,SAAS;AAC7CA,kBAAA,MAAA,MAAA,OAAA,mCAAY,sCAAsC,YAAY,MAAM;AAGpE,UAAM,cAAc;AAAA,MAClB,OAAOC,gBAAc,eAAC;AAAA,MACtB,OAAO;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,cACP;AAAA,gBACE,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWP;AAAA,cACD;AAAA,gBACE,OAAO,0BAA0B,WAAW;AAAA,cAC7C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD,YAAY;AAAA,QACV,eAAe;AAAA,MAChB;AAAA,IACP;AAEID,kBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AAGzC,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAKC,gBAAc,eAAC;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB,UAAUA,gBAAc,eAAC,OAAO;AAAA,MAClD;AAAA,MACD,SAASA,gBAAc,eAAC;AAAA,IAC9B,CAAK;AAEDD,kBAAY,MAAA,MAAA,OAAA,mCAAA,4BAA4B,SAAS,UAAU;AAC3DA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,gCAAgC,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC,CAAC;AAGlF,QAAI,SAAS,eAAe,KAAK;AAC/B,YAAM,IAAI,MAAM,4BAA4B,SAAS,UAAU,EAAE;AAAA,IAClE;AAGD,UAAM,eAAe,SAAS;AAC9BA,kBAAA,MAAA,MAAA,OAAA,mCAAY,iCAAiC,YAAY;AACzDA,kBAAA,MAAA,MAAA,OAAA,mCAAY,wCAAwC,6CAAc,MAAM;AAExE,QAAI,CAAC,gBAAgB,CAAC,aAAa,QAAQ;AACzC,YAAM,IAAI,MAAM,wBAAuB,6CAAc,YAAW,MAAM,EAAE;AAAA,IACzE;AAGDA,wBAAA,MAAA,OAAA,oCAAY,oCAAmC,kBAAa,WAAb,mBAAqB,OAAO;AAC3EA,wBAAA,MAAA,OAAA,oCAAY,gCAA+B,wBAAa,WAAb,mBAAqB,YAArB,mBAA+B,EAAE;AAC5EA,kBAAAA,uDAAY,wCAAuC,8BAAa,WAAb,mBAAqB,YAArB,mBAA+B,OAA/B,mBAAmC,OAAO;AAC7FA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,gDAA+C,oCAAa,WAAb,mBAAqB,YAArB,mBAA+B,OAA/B,mBAAmC,YAAnC,mBAA4C,OAAO;AAG9G,UAAM,oBAAmB,oCAAa,WAAb,mBAAqB,YAArB,mBAA+B,OAA/B,mBAAmC,YAAnC,mBAA4C;AACrEA,kBAAA,MAAA,MAAA,OAAA,oCAAY,wCAAwC,gBAAgB;AACpEA,kBAAA,MAAA,MAAA,OAAA,oCAAY,uCAAuC,OAAO,gBAAgB;AAE1E,QAAI,CAAC,kBAAkB;AAErBA,oBAAAA,uDAAY,6BAA6B;AACzCA,2EAAY,8CAA6C,kBAAa,WAAb,mBAAqB,IAAI;AAClFA,2EAAY,iDAAgD,kBAAa,WAAb,mBAAqB,OAAO;AACxFA,oBAAY,MAAA,MAAA,OAAA,oCAAA,yCAAyC,6CAAc,OAAO;AAE1E,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC3C;AAEDA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,wBAAwB;AACpCA,kBAAY,MAAA,MAAA,OAAA,oCAAA,2BAA2B,OAAO,gBAAgB;AAC9DA,kBAAA,MAAA,MAAA,OAAA,oCAAY,yBAAyB,gBAAgB;AAGrD,QAAI,cAAc;AAElB,QAAI,OAAO,qBAAqB,UAAU;AACxC,oBAAc;AAAA,IACf,WAAU,oBAAoB,OAAO,qBAAqB,UAAU;AACnEA,oBAAAA,uDAAY,+CAA+C;AAG3D,oBAAc,iBAAiB,QAC7B,iBAAiB,WACjB,iBAAiB,WACjB,iBAAiB,SACjB,iBAAiB;AAGnB,UAAI,OAAO,gBAAgB,YAAY,aAAa;AAClD,sBAAc,YAAY,QACxB,YAAY,WACZ,YAAY,WACZ,KAAK,UAAU,WAAW;AAAA,MAC7B;AAGD,UAAI,CAAC,eAAe,OAAO,gBAAgB,UAAU;AACnDA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,kCAAkC;AAC9C,sBAAc,KAAK,UAAU,gBAAgB;AAAA,MAC9C;AAAA,IACP,OAAW;AACL,oBAAc,OAAO,oBAAoB,EAAE;AAAA,IAC5C;AAEDA,yEAAY,2BAA2B,WAAW;AAClDA,kBAAA,MAAA,MAAA,OAAA,oCAAY,2BAA2B,OAAO,WAAW;AACzDA,kBAAA,MAAA,MAAA,OAAA,oCAAY,2BAA2B,YAAY,MAAM;AAGzD,UAAM,0BAA0B,4BAA4B,WAAW;AACvEA,kBAAY,MAAA,MAAA,OAAA,oCAAA,2BAA2B,uBAAuB;AAG9D,WAAO;AAAA,MACL,gBAAgB;AAAA;AAAA,MAChB,iBAAiB;AAAA;AAAA,IACvB;AAAA,EAEG,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,oCAAA,2BAA2B,KAAK;AAG9C,QAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AAChC,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACpC,WAAU,MAAM,QAAQ,SAAS,KAAK,GAAG;AACxC,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACzC,OAAW;AACL,YAAM,IAAI,MAAM,WAAW,MAAM,OAAO,EAAE;AAAA,IAC3C;AAAA,EACF;AACH;AAOO,SAAS,4BAA4B,gBAAgB;AAC1D,MAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;AACzD,WAAO;AAAA,EACR;AAED,MAAI;AAEF,QAAI,UAAU,eACX,QAAQ,QAAQ,GAAG,EACnB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,SAAS,EAAE,EACnB,QAAQ,OAAO,EAAE,EACjB,QAAQ,cAAc,EAAE,EACxB,QAAQ,YAAY,EAAE,EACtB,QAAQ,YAAY,EAAE,EACtB,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,GAAG,EACpB,QAAQ,QAAQ,GAAG,EACnB;AAGH,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAEI,QAAI,gBAAgB,CAAA;AAGpB,eAAW,WAAW,cAAc;AAClC,YAAM,QAAQ,QAAQ,MAAM,OAAO;AACnC,UAAI,SAAS,MAAM,CAAC,GAAG;AACrB,cAAM,OAAO,MAAM,CAAC,EAAE,KAAI;AAC1B,YAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK;AACxC,wBAAc,KAAK,IAAI;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAGD,QAAI,cAAc,WAAW,GAAG;AAC9B,YAAM,gBAAgB,QAAQ,MAAM,OAAO,EAAE,CAAC;AAC9C,UAAI,iBAAiB,cAAc,SAAS,IAAI;AAC9C,sBAAc,KAAK,aAAa;AAAA,MACjC;AAAA,IACF;AAGD,QAAI,cAAc,SAAS,GAAG;AAC5B,UAAI,SAAS,cAAc,CAAC;AAG5B,UAAI,CAAC,OAAO,MAAM,iBAAiB,GAAG;AACpC,iBAAS,QAAQ;AAAA,MAClB;AAGD,UAAI,CAAC,OAAO,SAAS,GAAG,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,CAAC,OAAO,SAAS,GAAG,GAAG;AAC3E,kBAAU;AAAA,MACX;AAGD,UAAI,OAAO,SAAS,IAAI;AACtB,iBAAS,OAAO,UAAU,GAAG,EAAE,IAAI;AAAA,MACpC;AAED,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,kBAAA,MAAA,MAAA,SAAA,oCAAc,mBAAmB,KAAK;AACtC,WAAO;AAAA,EACR;AACH;AAOO,eAAe,oBAAoB,WAAW;AACnD,MAAI;AACFA,kBAAAA,uDAAY,mCAAmC;AAE/C,UAAM,cAAc,MAAM,UAAU,SAAS;AAC7C,UAAM,cAAc;AAAA,MAClB,OAAOC,gBAAc,eAAC;AAAA,MACtB,OAAO;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,cACP;AAAA,gBACE,MAAM;AAAA,cACP;AAAA,cACD;AAAA,gBACE,OAAO,0BAA0B,WAAW;AAAA,cAC7C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD,YAAY;AAAA,QACV,eAAe;AAAA,MAChB;AAAA,IACP;AAEI,UAAM,WAAW,MAAMD,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAKC,gBAAc,eAAC;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB,UAAUA,gBAAc,eAAC,OAAO;AAAA,MAClD;AAAA,MACD,SAASA,gBAAc,eAAC;AAAA,IAC9B,CAAK;AAEDD,kBAAAA,MAAY,MAAA,OAAA,oCAAA,qBAAqB,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAClE,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,oCAAA,iBAAiB,KAAK;AACpC,UAAM;AAAA,EACP;AACH;;;;"}
{"version": 3, "file": "qwenVLService.js", "sources": ["utils/ai/qwenVLService.js"], "sourcesContent": ["// 通义千问VL-MAX图像识别服务\nimport { QWEN_VL_CONFIG } from './config.js';\n\n/**\n * 将图片转换为base64格式\n * @param {string} filePath 图片文件路径\n * @returns {Promise<string>} base64编码的图片数据\n */\nfunction getBase64(filePath) {\n  return new Promise((resolve, reject) => {\n    uni.getFileSystemManager().readFile({\n      filePath: filePath,\n      encoding: 'base64',\n      success: (res) => {\n        resolve(res.data);\n      },\n      fail: (err) => {\n        console.error('读取图片文件失败:', err);\n        reject(new Error('读取图片文件失败'));\n      }\n    });\n  });\n}\n\n/**\n * 调用通义千问VL-MAX进行食物图像识别\n * @param {string} imagePath 图片路径\n * @returns {Promise<string>} 图像描述结果\n */\nexport async function analyzeImageWithQwenVL(imagePath) {\n  try {\n    console.log('🔍 [通义千问VL-MAX] 开始分析食物图片...');\n    console.log('🔍 [通义千问VL-MAX] 图片路径:', imagePath);\n\n    // 获取图片的base64编码\n    const base64Image = await getBase64(imagePath);\n    console.log('🔍 [通义千问VL-MAX] 图片base64编码获取成功，长度:', base64Image.length);\n\n    // 构建请求数据\n    const requestData = {\n      model: QWEN_VL_CONFIG.MODEL,\n      input: {\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                text: `请详细分析这张食物图片，包括以下信息：\n1. 食物种类和名称（尽可能具体）\n2. 食物的数量和大小\n3. 食物的外观特征（颜色、形状、质地等）\n4. 烹饪方式（如炒、煮、蒸、烤等）\n5. 配菜和搭配情况\n6. 食物的新鲜程度和品质\n7. 估计的份量大小\n8. 可能的营养特点\n\n请用中文详细描述，描述越详细越好，这将用于后续的营养分析。`\n              },\n              {\n                image: `data:image/jpeg;base64,${base64Image}`\n              }\n            ]\n          }\n        ]\n      },\n      parameters: {\n        result_format: \"message\"\n      }\n    };\n\n    console.log('🔍 [通义千问VL-MAX] 发送请求到API...');\n\n    // 发送请求到通义千问VL-MAX API\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.BASE_URL,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`\n      },\n      timeout: QWEN_VL_CONFIG.TIMEOUT\n    });\n\n    console.log('🔍 [通义千问VL-MAX] API响应状态:', response.statusCode);\n    console.log('🔍 [通义千问VL-MAX] API响应数据:', response.data);\n\n    // 检查响应状态\n    if (response.statusCode !== 200) {\n      throw new Error(`通义千问VL-MAX API请求失败: HTTP ${response.statusCode}`);\n    }\n\n    // 解析响应数据\n    const responseData = response.data;\n    if (!responseData || !responseData.output) {\n      throw new Error(`通义千问VL-MAX API返回错误: ${responseData?.message || '未知错误'}`);\n    }\n\n    // 提取图像描述结果\n    const imageDescription = responseData.output?.choices?.[0]?.message?.content;\n    if (!imageDescription) {\n      throw new Error('通义千问VL-MAX API返回的数据格式不正确');\n    }\n\n    console.log('🔍 [通义千问VL-MAX] 图像识别成功');\n    console.log('🔍 [通义千问VL-MAX] 识别结果:', imageDescription);\n\n    return imageDescription;\n\n  } catch (error) {\n    console.error('🔍 [通义千问VL-MAX] 图像识别失败:', error);\n\n    // 根据错误类型提供不同的错误信息\n    if (error.message.includes('网络')) {\n      throw new Error('网络连接失败，请检查网络设置后重试');\n    } else if (error.message.includes('API')) {\n      throw new Error('图像识别服务暂时不可用，请稍后重试');\n    } else {\n      throw new Error(`图像识别失败: ${error.message}`);\n    }\n  }\n}\n\n/**\n * 测试通义千问VL-MAX API连接\n * @returns {Promise<boolean>} 测试结果\n */\nexport async function testQwenVLConnection() {\n  try {\n    console.log('🔍 [通义千问VL-MAX] 测试API连接...');\n\n    // 创建一个简单的测试请求\n    const testData = {\n      model: QWEN_VL_CONFIG.MODEL,\n      input: {\n        messages: [\n          {\n            role: \"user\",\n            content: [\n              {\n                text: \"测试连接\"\n              }\n            ]\n          }\n        ]\n      }\n    };\n\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.BASE_URL,\n      method: 'POST',\n      data: testData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.API_KEY}`\n      },\n      timeout: 10000\n    });\n\n    console.log('🔍 [通义千问VL-MAX] 连接测试结果:', response.statusCode);\n    return response.statusCode === 200;\n\n  } catch (error) {\n    console.error('🔍 [通义千问VL-MAX] 连接测试失败:', error);\n    return false;\n  }\n}\n"], "names": ["uni", "QWEN_VL_CONFIG"], "mappings": ";;;AAQA,SAAS,UAAU,UAAU;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,wBAAI,qBAAsB,EAAC,SAAS;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,SAAS,CAAC,QAAQ;AAChB,gBAAQ,IAAI,IAAI;AAAA,MACjB;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,GAAG;AAC9B,eAAO,IAAI,MAAM,UAAU,CAAC;AAAA,MAC7B;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAOO,eAAe,uBAAuB,WAAW;;AACtD,MAAI;AACFA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AACzCA,kBAAY,MAAA,MAAA,OAAA,mCAAA,yBAAyB,SAAS;AAG9C,UAAM,cAAc,MAAM,UAAU,SAAS;AAC7CA,kBAAA,MAAA,MAAA,OAAA,mCAAY,sCAAsC,YAAY,MAAM;AAGpE,UAAM,cAAc;AAAA,MAClB,OAAOC,gBAAc,eAAC;AAAA,MACtB,OAAO;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,cACP;AAAA,gBACE,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWP;AAAA,cACD;AAAA,gBACE,OAAO,0BAA0B,WAAW;AAAA,cAC7C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD,YAAY;AAAA,QACV,eAAe;AAAA,MAChB;AAAA,IACP;AAEID,kBAAAA,MAAA,MAAA,OAAA,mCAAY,6BAA6B;AAGzC,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAKC,gBAAc,eAAC;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB,UAAUA,gBAAc,eAAC,OAAO;AAAA,MAClD;AAAA,MACD,SAASA,gBAAc,eAAC;AAAA,IAC9B,CAAK;AAEDD,kBAAY,MAAA,MAAA,OAAA,mCAAA,4BAA4B,SAAS,UAAU;AAC3DA,kBAAA,MAAA,MAAA,OAAA,mCAAY,4BAA4B,SAAS,IAAI;AAGrD,QAAI,SAAS,eAAe,KAAK;AAC/B,YAAM,IAAI,MAAM,4BAA4B,SAAS,UAAU,EAAE;AAAA,IAClE;AAGD,UAAM,eAAe,SAAS;AAC9B,QAAI,CAAC,gBAAgB,CAAC,aAAa,QAAQ;AACzC,YAAM,IAAI,MAAM,wBAAuB,6CAAc,YAAW,MAAM,EAAE;AAAA,IACzE;AAGD,UAAM,oBAAmB,oCAAa,WAAb,mBAAqB,YAArB,mBAA+B,OAA/B,mBAAmC,YAAnC,mBAA4C;AACrE,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC3C;AAEDA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,wBAAwB;AACpCA,kBAAA,MAAA,MAAA,OAAA,oCAAY,yBAAyB,gBAAgB;AAErD,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,oCAAA,2BAA2B,KAAK;AAG9C,QAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AAChC,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACpC,WAAU,MAAM,QAAQ,SAAS,KAAK,GAAG;AACxC,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACzC,OAAW;AACL,YAAM,IAAI,MAAM,WAAW,MAAM,OAAO,EAAE;AAAA,IAC3C;AAAA,EACF;AACH;;"}
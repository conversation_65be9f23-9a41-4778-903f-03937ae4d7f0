{"version": 3, "file": "food-analysis.js", "sources": ["pages/blood-glucose/food-analysis.vue", "D:/bc Files/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYmxvb2QtZ2x1Y29zZS9mb29kLWFuYWx5c2lzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"page-container\">\r\n\t\t<navbar :isHomePage=\"false\" title=\"饮食分析\" />\r\n\r\n\t\t<!-- 主体内容 -->\r\n\t\t<view class=\"content-container\" :style=\"contentStyle\">\r\n\r\n\t\t\t<!-- 上传区域 -->\r\n\t\t\t<view class=\"upload-section\">\r\n\t\t\t\t<view class=\"upload-header\">\r\n\t\t\t\t\t<text class=\"upload-title\">AI食物识别</text>\r\n\t\t\t\t\t<text class=\"upload-subtitle\">拍照或选择图片，AI将分析食物营养成分</text>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<view class=\"upload-area\" @tap=\"chooseImage\">\r\n\t\t\t\t\t<image v-if=\"selectedImage\" :src=\"selectedImage\" class=\"preview-image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view v-else class=\"upload-placeholder\">\r\n\t\t\t\t\t\t<image src=\"../../static/resource/images/camera.png\" class=\"upload-icon\"></image>\r\n\t\t\t\t\t\t<text class=\"upload-text\">点击上传食物图片</text>\r\n\t\t\t\t\t\t<text class=\"upload-hint\">支持JPG、PNG格式</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"upload-actions\" v-if=\"selectedImage\">\r\n\t\t\t\t\t<button class=\"action-btn secondary\" @tap=\"reSelectImage\">重新选择</button>\r\n\t\t\t\t\t<button class=\"action-btn primary\" @tap=\"analyzeFood\" :disabled=\"analyzing\">\r\n\t\t\t\t\t\t{{ analyzing ? '分析中...' : '开始分析' }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 分析状态提示 -->\r\n\t\t\t\t<view class=\"analysis-status\" v-if=\"analyzing || errorMessage\">\r\n\t\t\t\t\t<view class=\"status-item\" v-if=\"analyzing\">\r\n\t\t\t\t\t\t<view class=\"loading-icon\"></view>\r\n\t\t\t\t\t\t<text class=\"status-text\">{{ currentStep }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"error-item\" v-if=\"errorMessage\">\r\n\t\t\t\t\t\t<text class=\"error-icon\">⚠️</text>\r\n\t\t\t\t\t\t<text class=\"error-text\">{{ errorMessage }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 分析结果 -->\r\n\t\t\t<view class=\"analysis-result\" v-if=\"analysisResult\">\r\n\t\t\t\t<view class=\"result-header\">\r\n\t\t\t\t\t<text class=\"result-title\">分析结果</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 识别的食物 -->\r\n\t\t\t\t<view class=\"food-info\">\r\n\t\t\t\t\t<view class=\"food-name\">\r\n\t\t\t\t\t\t<text class=\"name-text\">{{ analysisResult.foodName }}</text>\r\n\t\t\t\t\t\t<view class=\"confidence-badge\">\r\n\t\t\t\t\t\t\t<text class=\"confidence-text\">置信度: {{ analysisResult.confidence }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 营养成分 -->\r\n\t\t\t\t<view class=\"nutrition-section\">\r\n\t\t\t\t\t<view class=\"section-title\">营养成分 (每100g)</view>\r\n\t\t\t\t\t<view class=\"nutrition-grid\">\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">热量</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.calories }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">kcal</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">碳水化合物</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.carbs }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">蛋白质</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.protein }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">脂肪</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fat }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">膳食纤维</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fiber }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">糖分</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.sugar }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- GI值提示 -->\r\n\t\t\t\t<view class=\"gi-section\">\r\n\t\t\t\t\t<view class=\"gi-header\">\r\n\t\t\t\t\t\t<text class=\"gi-title\">GI值分析</text>\r\n\t\t\t\t\t\t<view class=\"gi-badge\" :class=\"getGIClass(analysisResult.giValue)\">\r\n\t\t\t\t\t\t\t<text class=\"gi-value\">GI: {{ analysisResult.giValue }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gi-description\">\r\n\t\t\t\t\t\t<text class=\"gi-text\">{{ getGIDescription(analysisResult.giValue) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 详细分析 -->\r\n\t\t\t\t<view class=\"detailed-analysis\" v-if=\"analysisResult.detailedAnalysis\">\r\n\r\n\t\t\t\t\t<!-- 健康建议 -->\r\n\t\t\t\t\t<view class=\"advice-section\">\r\n\t\t\t\t\t\t<view class=\"advice-header\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/resource/images/tips.png\" class=\"advice-icon\"></image>\r\n\t\t\t\t\t\t\t<text class=\"advice-title\">健康建议</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"advice-content-unified\">\r\n\t\t\t\t\t\t\t<text class=\"advice-text-unified\">\r\n\t\t\t\t\t\t\t\t{{ analysisResult.healthAdvice.join('；') }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 推荐食用 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header can-eat\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">✅ 推荐食用</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.canEat.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 需要控制 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header limit-eat\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">⚠️ 需要控制</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.limitEat.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 改进建议 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header suggestions\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">💡 改进建议</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\tclass=\"unified-text\">{{ analysisResult.detailedAnalysis.suggestions.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<!-- 保存记录 -->\r\n\t\t\t\t<view class=\"save-section\">\r\n\t\t\t\t\t<button class=\"save-btn\" @tap=\"saveAnalysisRecord\">保存到饮食记录</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 历史记录 -->\r\n\t\t\t<view class=\"history-section\" v-if=\"historyRecords.length > 0\">\r\n\t\t\t\t<view class=\"history-header\">\r\n\t\t\t\t\t<text class=\"history-title\">最近分析</text>\r\n\t\t\t\t\t<text class=\"view-all\" @tap=\"viewAllHistory\">查看全部</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"history-list\">\r\n\t\t\t\t\t<view class=\"history-item\" v-for=\"record in historyRecords.slice(0, 3)\" :key=\"record.id\"\r\n\t\t\t\t\t\t@tap=\"viewHistoryDetail(record)\">\r\n\t\t\t\t\t\t<image :src=\"record.image\" class=\"history-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"history-info\">\r\n\t\t\t\t\t\t\t<text class=\"history-food-name\">{{ record.foodName }}</text>\r\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ formatDate(record.timestamp) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"history-gi\" :class=\"getGIClass(record.giValue)\">\r\n\t\t\t\t\t\t\t<text class=\"history-gi-text\">GI: {{ record.giValue }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\n\timport {\r\n\t\tref,\r\n\t\tcomputed,\r\n\t\tonMounted\r\n\t} from 'vue'\r\n\timport navbar from '../../components/navbar/navbar.vue'\r\n\timport {\r\n\t\tsendMessageToAI\r\n\t} from '../../utils/ai/service'\r\n\r\n\r\n\r\n\t// 响应式数据\r\n\tconst selectedImage = ref('')\r\n\tconst analyzing = ref(false)\r\n\tconst analysisResult = ref(null)\r\n\tconst historyRecords = ref([])\r\n\tconst currentStep = ref('') // 当前处理步骤\r\n\tconst errorMessage = ref('') // 错误信息\r\n\r\n\t// 计算属性\r\n\tconst contentStyle = computed(() => {\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n\t\tconst navHeight = menuButtonInfo.top + menuButtonInfo.height + 8\r\n\t\treturn {\r\n\t\t\tpaddingTop: navHeight + 'px'\r\n\t\t}\r\n\t})\r\n\r\n\t// 方法\r\n\tconst chooseImage = () => {\r\n\t\tuni.chooseImage({\r\n\t\t\tcount: 1,\r\n\t\t\tsizeType: ['compressed'],\r\n\t\t\tsourceType: ['camera', 'album'],\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tselectedImage.value = res.tempFilePaths[0]\r\n\t\t\t\tanalysisResult.value = null\r\n\t\t\t\tconsole.log('图片选择成功:', res.tempFilePaths[0])\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tconsole.error('选择图片失败:', err)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '选择图片失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\r\n\tconst reSelectImage = () => {\r\n\t\tselectedImage.value = ''\r\n\t\tanalysisResult.value = null\r\n\t}\r\n\r\n\t/**\r\n\t * AI分析食物营养成分\r\n\t */\r\n\tconst analyzeFood = async () => {\r\n\t\tif (!selectedImage.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请先选择图片',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tanalyzing.value = true\r\n\r\n\t\ttry {\r\n\t\t\terrorMessage.value = ''\r\n\r\n\t\t\t// 图片质量预检查\r\n\t\t\tcurrentStep.value = '正在检查图片质量...'\r\n\t\t\tconsole.log('开始图片质量检查...')\r\n\r\n\t\t\t// 检查图片文件大小（简单的质量指标）\r\n\t\t\ttry {\r\n\t\t\t\tconst fileInfo = await new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.getFileInfo({\r\n\t\t\t\t\t\tfilePath: selectedImage.value,\r\n\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\r\n\t\t\t\tconsole.log('图片文件信息:', fileInfo)\r\n\r\n\t\t\t\t// 检查文件大小（太小可能质量不佳，太大可能处理困难）\r\n\t\t\t\tif (fileInfo.size < 10000) { // 小于10KB\r\n\t\t\t\t\tconsole.warn('图片文件过小，可能质量不佳')\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '图片质量可能不佳，建议重新拍摄',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (fileInfo.size > 10000000) { // 大于10MB\r\n\t\t\t\t\tconsole.warn('图片文件过大')\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '图片文件较大，处理可能较慢',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t} catch (fileError) {\r\n\t\t\t\tconsole.warn('无法获取文件信息:', fileError)\r\n\t\t\t}\r\n\r\n\t\t\t// 第一步：使用百度AI综合识别图片中的食物\r\n\t\t\tcurrentStep.value = '正在识别食物...'\r\n\t\t\tconsole.log('开始使用百度AI识别图片中的食物...')\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在识别食物...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\r\n\t\t\tconst baiduResult = await comprehensiveFoodRecognition(selectedImage.value)\r\n\t\t\tconsole.log('百度AI识别结果:', baiduResult)\r\n\r\n\t\t\t// 构建详细的食物描述\r\n\t\t\tlet foodDescription = ''\r\n\t\t\tlet recognizedFoods = []\r\n\t\t\tlet confidence = 0\r\n\r\n\t\t\t// 检查百度AI识别结果并处理\r\n\t\t\tif (baiduResult && baiduResult.success) {\r\n\t\t\t\t// 处理菜品识别结果\r\n\t\t\t\tif (baiduResult.data.dishResults && baiduResult.data.dishResults.length > 0) {\r\n\t\t\t\t\tconst dishItems = baiduResult.data.dishResults\r\n\t\t\t\t\t\t.filter(item => item.confidence > 0.3)\r\n\t\t\t\t\t\t.map(item => `${item.name}(置信度:${Math.round(item.confidence * 100)}%)`)\r\n\t\t\t\t\trecognizedFoods.push(...dishItems)\r\n\t\t\t\t\tconfidence = Math.max(confidence, baiduResult.data.dishResults[0].confidence * 100)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 处理果蔬识别结果\r\n\t\t\t\tif (baiduResult.data.ingredientResults && baiduResult.data.ingredientResults.length > 0) {\r\n\t\t\t\t\tconst ingredientItems = baiduResult.data.ingredientResults\r\n\t\t\t\t\t\t.filter(item => item.confidence > 0.3)\r\n\t\t\t\t\t\t.map(item => `${item.name}(置信度:${Math.round(item.confidence * 100)}%)`)\r\n\t\t\t\t\trecognizedFoods.push(...ingredientItems)\r\n\t\t\t\t\tconfidence = Math.max(confidence, baiduResult.data.ingredientResults[0].confidence * 100)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 构建食物描述\r\n\t\t\t\tif (recognizedFoods.length > 0) {\r\n\t\t\t\t\tfoodDescription = `图片中识别到的食物：${recognizedFoods.join('、')}`\r\n\r\n\t\t\t\t\t// 添加图像理解的详细信息\r\n\t\t\t\t\tif (baiduResult.data.summary) {\r\n\t\t\t\t\t\tfoodDescription += `\\n\\n图像场景分析：${baiduResult.data.summary}`\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加关键词信息\r\n\t\t\t\t\tif (baiduResult.data.allKeywords && baiduResult.data.allKeywords.length > 0) {\r\n\t\t\t\t\t\tfoodDescription += `\\n\\n相关关键词：${baiduResult.data.allKeywords.join('、')}`\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 识别成功但置信度过低的降级处理\r\n\t\t\t\t\tconsole.warn('百度AI识别成功但置信度过低')\r\n\t\t\t\t\tfoodDescription = '图片中包含食物，但具体类型需要进一步分析。请确保图片清晰且食物占据画面主要部分。'\r\n\t\t\t\t\tconfidence = 30\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 百度AI识别失败的降级处理\r\n\t\t\t\tconsole.warn('百度AI识别失败，使用降级策略')\r\n\t\t\t\tif (baiduResult && baiduResult.error) {\r\n\t\t\t\t\tconsole.error('百度AI错误信息:', baiduResult.error)\r\n\t\t\t\t}\r\n\t\t\t\tfoodDescription = '图片中包含食物，正在进行营养分析。由于识别服务暂时不可用，将基于通用食物数据进行分析。'\r\n\t\t\t\tconfidence = 25\r\n\t\t\t}\r\n\r\n\t\t\t// 置信度过低的额外处理\r\n\t\t\tif (confidence < 30) {\r\n\t\t\t\tconsole.warn(`识别置信度过低: ${confidence}%`)\r\n\t\t\t\tcurrentStep.value = '识别置信度较低，正在使用通用分析...'\r\n\t\t\t}\r\n\r\n\t\t\tconsole.log('食物描述:', foodDescription)\r\n\t\t\tconsole.log('识别置信度:', confidence)\r\n\r\n\t\t\t// 第二步：将识别结果发送给DeepSeek进行营养分析\r\n\t\t\tcurrentStep.value = '正在分析营养成分...'\r\n\t\t\tconsole.log('开始AI营养分析...')\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在分析营养成分...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\r\n\t\t\tconst aiPrompt = `作为专业的营养师，请分析以下食物的营养成分和健康建议：\r\n\r\n${foodDescription}\r\n\r\n请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：\r\n\r\n{\r\n  \"foodName\": \"食物名称\",\r\n  \"confidence\": 85,\r\n  \"giValue\": 65,\r\n  \"nutrition\": {\r\n    \"calories\": 180,\r\n    \"carbs\": 35,\r\n    \"protein\": 8,\r\n    \"fat\": 6,\r\n    \"fiber\": 4,\r\n    \"sugar\": 2\r\n  },\r\n  \"healthAdvice\": [\r\n    \"健康建议1\",\r\n    \"健康建议2\",\r\n    \"健康建议3\"\r\n  ],\r\n  \"detailedAnalysis\": {\r\n    \"canEat\": [\r\n      \"推荐食用的食物及原因\"\r\n    ],\r\n    \"limitEat\": [\r\n      \"需要限制的食物及原因\"\r\n    ],\r\n    \"suggestions\": [\r\n      \"具体的饮食建议\"\r\n    ]\r\n  }\r\n}\r\n\r\n要求：\r\n1. 所有数值必须是合理的营养数据\r\n2. GI值范围在0-100之间\r\n3. 健康建议要针对糖尿病患者\r\n4. 分析要专业且实用\r\n5. 返回纯JSON格式，不要markdown代码块`\r\n\r\n\t\t\tconst aiResponse = await sendMessageToAI(aiPrompt)\r\n\t\t\tconsole.log('AI分析结果:', aiResponse)\r\n\r\n\t\t\t// 第三步：解析AI返回的JSON结果\r\n\t\t\tlet parsedResult\r\n\t\t\ttry {\r\n\t\t\t\t// 清理可能的markdown格式\r\n\t\t\t\tconst cleanedResponse = aiResponse.replace(/```json\\s*|\\s*```/g, '').trim()\r\n\t\t\t\tparsedResult = JSON.parse(cleanedResponse)\r\n\t\t\t} catch (parseError) {\r\n\t\t\t\tconsole.error('JSON解析失败:', parseError)\r\n\t\t\t\t// 使用备用数据结构\r\n\t\t\t\tparsedResult = {\r\n\t\t\t\t\tfoodName: foodDescription.replace('图片中识别到的食物：', '') || \"识别的食物\",\r\n\t\t\t\t\tconfidence: 80,\r\n\t\t\t\t\tgiValue: 60,\r\n\t\t\t\t\tnutrition: {\r\n\t\t\t\t\t\tcalories: 150,\r\n\t\t\t\t\t\tcarbs: 30,\r\n\t\t\t\t\t\tprotein: 6,\r\n\t\t\t\t\t\tfat: 5,\r\n\t\t\t\t\t\tfiber: 3,\r\n\t\t\t\t\t\tsugar: 8\r\n\t\t\t\t\t},\r\n\t\t\t\t\thealthAdvice: [\r\n\t\t\t\t\t\t\"请注意控制食用份量\",\r\n\t\t\t\t\t\t\"建议搭配低GI食物一起食用\",\r\n\t\t\t\t\t\t\"餐后适当运动有助于血糖控制\"\r\n\t\t\t\t\t],\r\n\t\t\t\t\tdetailedAnalysis: {\r\n\t\t\t\t\t\tcanEat: [\"根据识别结果，适量食用有益健康\"],\r\n\t\t\t\t\t\tlimitEat: [\"注意控制高糖高脂食物的摄入\"],\r\n\t\t\t\t\t\tsuggestions: [\"建议咨询专业营养师制定个性化饮食方案\"]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 设置分析结果\r\n\t\t\tanalysisResult.value = parsedResult\r\n\r\n\t\t\t// 保存到历史记录\r\n\t\t\tconst newRecord = {\r\n\t\t\t\tid: Date.now(),\r\n\t\t\t\timage: selectedImage.value,\r\n\t\t\t\tfoodName: analysisResult.value.foodName,\r\n\t\t\t\tgiValue: analysisResult.value.giValue,\r\n\t\t\t\ttimestamp: Date.now(),\r\n\t\t\t\tconfidence: analysisResult.value.confidence,\r\n\t\t\t\tnutrition: analysisResult.value.nutrition,\r\n\t\t\t\thealthAdvice: analysisResult.value.healthAdvice,\r\n\t\t\t\tdetailedAnalysis: analysisResult.value.detailedAnalysis,\r\n\t\t\t\tdate: new Date().toLocaleDateString('zh-CN', {\r\n\t\t\t\t\tmonth: 'numeric',\r\n\t\t\t\t\tday: 'numeric',\r\n\t\t\t\t\thour: '2-digit',\r\n\t\t\t\t\tminute: '2-digit'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tsaveHistoryRecord(newRecord)\r\n\r\n\t\t\tuni.hideLoading()\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分析完成',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('分析失败:', error)\r\n\t\t\tuni.hideLoading()\r\n\r\n\t\t\t// 根据错误类型提供不同的提示\r\n\t\t\tlet errorTitle = '分析失败，请重试'\r\n\t\t\tif (error.message && error.message.includes('网络')) {\r\n\t\t\t\terrorTitle = '网络连接失败，请检查网络'\r\n\t\t\t\terrorMessage.value = '请检查网络连接后重试'\r\n\t\t\t} else if (error.message && error.message.includes('识别')) {\r\n\t\t\t\terrorTitle = '图片识别失败'\r\n\t\t\t\terrorMessage.value = '请选择清晰的食物图片重试'\r\n\t\t\t} else if (error.message && error.message.includes('AI')) {\r\n\t\t\t\terrorTitle = 'AI分析服务暂时不可用'\r\n\t\t\t\terrorMessage.value = '服务暂时繁忙，请稍后重试'\r\n\t\t\t} else {\r\n\t\t\t\terrorMessage.value = '分析过程中出现错误，请重新尝试'\r\n\t\t\t}\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: errorTitle,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t})\r\n\t\t} finally {\r\n\t\t\tanalyzing.value = false\r\n\t\t\tcurrentStep.value = ''\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取GI值等级样式类\r\n\tconst getGIClass = (giValue) => {\r\n\t\tif (giValue <= 55) return 'low'\r\n\t\tif (giValue <= 70) return 'medium'\r\n\t\treturn 'high'\r\n\t}\r\n\r\n\t// 获取GI值描述\r\n\tconst getGIDescription = (giValue) => {\r\n\t\tif (giValue <= 55) return '低GI食物，对血糖影响较小，适合糖尿病患者食用'\r\n\t\tif (giValue <= 70) return '中等GI食物，适量食用，建议搭配低GI食物'\r\n\t\treturn '高GI食物，会快速升高血糖，糖尿病患者需严格控制'\r\n\t}\r\n\r\n\t// 备用分析结果生成\r\n\tconst generateBackupAnalysis = (foodName) => {\r\n\t\tconst foodDatabase = {\r\n\t\t\t'白米饭': {\r\n\t\t\t\tfoodName: '白米饭',\r\n\t\t\t\tconfidence: 95,\r\n\t\t\t\tgiValue: 83,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 130,\r\n\t\t\t\t\tcarbs: 28,\r\n\t\t\t\t\tprotein: 2.7,\r\n\t\t\t\t\tfat: 0.3,\r\n\t\t\t\t\tfiber: 0.4,\r\n\t\t\t\t\tsugar: 0.1\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['属于高GI食物，会快速升高血糖', '建议搭配蔬菜和蛋白质一起食用', '可以选择糙米或杂粮饭作为替代']\r\n\t\t\t},\r\n\t\t\t'苹果': {\r\n\t\t\t\tfoodName: '苹果',\r\n\t\t\t\tconfidence: 92,\r\n\t\t\t\tgiValue: 36,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 52,\r\n\t\t\t\t\tcarbs: 14,\r\n\t\t\t\t\tprotein: 0.3,\r\n\t\t\t\t\tfat: 0.2,\r\n\t\t\t\t\tfiber: 2.4,\r\n\t\t\t\t\tsugar: 10\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['属于低GI食物，对血糖影响较小', '富含膳食纤维，有助于血糖稳定', '建议在两餐之间食用，避免空腹']\r\n\t\t\t},\r\n\t\t\t'糙米饭': {\r\n\t\t\t\tfoodName: '糙米饭',\r\n\t\t\t\tconfidence: 90,\r\n\t\t\t\tgiValue: 50,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 112,\r\n\t\t\t\t\tcarbs: 23,\r\n\t\t\t\t\tprotein: 2.6,\r\n\t\t\t\t\tfat: 0.9,\r\n\t\t\t\t\tfiber: 1.8,\r\n\t\t\t\t\tsugar: 0.4\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['中等GI食物，比白米饭更适合糖尿病患者', '富含膳食纤维，有助于血糖稳定', '建议控制食用量，搭配蔬菜食用']\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn foodDatabase[foodName] || {\r\n\t\t\tfoodName: foodName,\r\n\t\t\tconfidence: 85,\r\n\t\t\tgiValue: 55,\r\n\t\t\tnutrition: {\r\n\t\t\t\tcalories: 100,\r\n\t\t\t\tcarbs: 20,\r\n\t\t\t\tprotein: 3,\r\n\t\t\t\tfat: 1,\r\n\t\t\t\tfiber: 2,\r\n\t\t\t\tsugar: 5\r\n\t\t\t},\r\n\t\t\thealthAdvice: ['请咨询营养师获取准确信息', '建议适量食用', '注意监测血糖变化']\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\tconst saveAnalysisRecord = () => {\r\n\t\tif (!analysisResult.value) return\r\n\r\n\t\tconst record = {\r\n\t\t\tid: Date.now(),\r\n\t\t\timage: selectedImage.value,\r\n\t\t\tfoodName: analysisResult.value.foodName,\r\n\t\t\tconfidence: analysisResult.value.confidence,\r\n\t\t\tgiValue: analysisResult.value.giValue,\r\n\t\t\tnutrition: analysisResult.value.nutrition,\r\n\t\t\thealthAdvice: analysisResult.value.healthAdvice,\r\n\t\t\ttimestamp: new Date().toISOString()\r\n\t\t}\r\n\r\n\t\tconst existingRecords = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\texistingRecords.unshift(record)\r\n\t\tuni.setStorageSync('foodAnalysisRecords', existingRecords)\r\n\r\n\t\thistoryRecords.value = existingRecords\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '保存成功',\r\n\t\t\ticon: 'success'\r\n\t\t})\r\n\t}\r\n\r\n\tconst loadHistoryRecords = () => {\r\n\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\thistoryRecords.value = records\r\n\t}\r\n\r\n\tconst saveHistoryRecord = (record) => {\r\n\t\ttry {\r\n\t\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\t\trecords.unshift(record)\r\n\t\t\t// 只保留最近50条记录\r\n\t\t\tif (records.length > 50) {\r\n\t\t\t\trecords.splice(50)\r\n\t\t\t}\r\n\t\t\tuni.setStorageSync('foodAnalysisRecords', records)\r\n\t\t\thistoryRecords.value = records\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('保存历史记录失败:', error)\r\n\t\t}\r\n\t}\r\n\r\n\tconst formatDate = (timestamp) => {\r\n\t\tconst date = new Date(timestamp)\r\n\t\tconst month = date.getMonth() + 1\r\n\t\tconst day = date.getDate()\r\n\t\tconst hours = date.getHours().toString().padStart(2, '0')\r\n\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\r\n\t\treturn `${month}月${day}日 ${hours}:${minutes}`\r\n\t}\r\n\r\n\tconst viewHistoryDetail = (record) => {\r\n\t\t// 显示历史记录详情\r\n\t\tanalysisResult.value = record\r\n\t\tselectedImage.value = record.image\r\n\t}\r\n\r\n\tconst viewAllHistory = () => {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/blood-glucose/food-history'\r\n\t\t})\r\n\t}\r\n\r\n\tonMounted(() => {\r\n\t\tloadHistoryRecords()\r\n\t})\r\n</script>\r\n\r\n<style scoped>\r\n\t.page-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f7fa;\r\n\t}\r\n\r\n\t.content-container {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t/* 上传区域 */\r\n\t.upload-section {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.upload-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.upload-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.upload-subtitle {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.upload-area {\r\n\t\tborder: 3rpx dashed #e0e0e0;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmin-height: 400rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\r\n\t.upload-area:active {\r\n\t\tborder-color: #00b38a;\r\n\t\tbackground: rgba(0, 179, 138, 0.05);\r\n\t}\r\n\r\n\t.preview-image {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.upload-placeholder {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.upload-icon {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.upload-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.upload-hint {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.upload-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\tflex: 1;\r\n\t\tpadding: 24rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.action-btn.secondary {\r\n\t\tbackground: #f5f7fa;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.action-btn.primary {\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn:disabled {\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t/* 分析结果 */\r\n\t.analysis-result {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.result-header {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t/* 食物信息 */\r\n\t.food-info {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.food-name {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.name-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.confidence-badge {\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.confidence-text {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t/* 营养成分 */\r\n\t.nutrition-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.nutrition-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: 1fr 1fr;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.nutrition-item {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.nutrition-label {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.nutrition-value {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #00b38a;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\r\n\t.nutrition-unit {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t/* GI值分析 */\r\n\t.gi-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.gi-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.gi-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.gi-badge {\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.gi-badge.low {\r\n\t\tbackground: #4CAF50;\r\n\t}\r\n\r\n\t.gi-badge.medium {\r\n\t\tbackground: #ff9800;\r\n\t}\r\n\r\n\t.gi-badge.high {\r\n\t\tbackground: #f44336;\r\n\t}\r\n\r\n\t.gi-value {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.gi-description {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f0f8ff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.gi-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t/* 健康建议 */\r\n\t.advice-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.advice-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.advice-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.advice-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.advice-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.advice-text {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #fff3cd;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #ffc107;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #856404;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.advice-content-unified {\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.advice-text-unified {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.8;\r\n\t\ttext-align: justify;\r\n\t}\r\n\r\n\t/* 详细分析 */\r\n\t.detailed-analysis {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.analysis-section {\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.section-header {\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.section-header.can-eat {\r\n\t\tbackground: rgba(76, 175, 80, 0.1);\r\n\t\tborder-left: 6rpx solid #4CAF50;\r\n\t}\r\n\r\n\t.section-header.limit-eat {\r\n\t\tbackground: rgba(255, 152, 0, 0.1);\r\n\t\tborder-left: 6rpx solid #ff9800;\r\n\t}\r\n\r\n\t.section-header.suggestions {\r\n\t\tbackground: rgba(0, 179, 138, 0.1);\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.food-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.food-item {\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder-left: 4rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.food-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.unified-content {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.unified-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: justify;\r\n\t}\r\n\r\n\t/* 保存按钮 */\r\n\t.save-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.save-btn {\r\n\t\twidth: 100%;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t/* 历史记录 */\r\n\t.history-section {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.history-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.history-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.view-all {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #00b38a;\r\n\t}\r\n\r\n\t.history-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.history-image {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.history-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.history-food-name {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.history-date {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.history-gi {\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.history-gi.low {\r\n\t\tbackground: #4CAF50;\r\n\t}\r\n\r\n\t.history-gi.medium {\r\n\t\tbackground: #ff9800;\r\n\t}\r\n\r\n\t.history-gi.high {\r\n\t\tbackground: #f44336;\r\n\t}\r\n\r\n\t.history-gi-text {\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\t/* 分析状态样式 */\r\n\t.analysis-status {\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.status-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.loading-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tborder: 3rpx solid #e0e0e0;\r\n\t\tborder-top: 3rpx solid #3b82f6;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% { transform: rotate(0deg); }\r\n\t\t100% { transform: rotate(360deg); }\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #3b82f6;\r\n\t}\r\n\r\n\t.error-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.error-icon {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.error-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #f44336;\r\n\t\tflex: 1;\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/2025077166-源代码/pz/pages/blood-glucose/food-analysis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "sendMessageToAI", "onMounted"], "mappings": ";;;;;;;AAmMC,MAAM,SAAS,MAAW;;;;AAQ1B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAC/B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3B,UAAM,eAAeC,cAAAA,SAAS,MAAM;AAChBC,oBAAAA,MAAI,kBAAmB;AAC1C,YAAM,iBAAiBA,cAAG,MAAC,gCAAiC;AAC5D,YAAM,YAAY,eAAe,MAAM,eAAe,SAAS;AAC/D,aAAO;AAAA,QACN,YAAY,YAAY;AAAA,MACxB;AAAA,IACH,CAAE;AAGD,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,wBAAc,QAAQ,IAAI,cAAc,CAAC;AACzC,yBAAe,QAAQ;AACvBA,8BAAY,MAAA,OAAA,gDAAA,WAAW,IAAI,cAAc,CAAC,CAAC;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAAA,qEAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AAAA,QACD;AAAA,MACJ,CAAG;AAAA,IACD;AAED,UAAM,gBAAgB,MAAM;AAC3B,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AAAA,IACvB;AAKD,UAAM,cAAc,YAAY;AAC/B,UAAI,CAAC,cAAc,OAAO;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD;AAAA,MACA;AAED,gBAAU,QAAQ;AAElB,UAAI;AACH,qBAAa,QAAQ;AAGrB,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,aAAa;AAGzB,YAAI;AACH,gBAAM,WAAW,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvDA,0BAAAA,MAAI,YAAY;AAAA,cACf,UAAU,cAAc;AAAA,cACxB,SAAS;AAAA,cACT,MAAM;AAAA,YACZ,CAAM;AAAA,UACN,CAAK;AAEDA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,WAAW,QAAQ;AAG/B,cAAI,SAAS,OAAO,KAAO;AAC1BA,0BAAAA,oEAAa,eAAe;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAChB,CAAM;AAAA,UACN,WAAe,SAAS,OAAO,KAAU;AACpCA,0BAAAA,oEAAa,QAAQ;AACrBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAChB,CAAM;AAAA,UACD;AAAA,QACD,SAAQ,WAAW;AACnBA,wBAAAA,MAAa,MAAA,QAAA,gDAAA,aAAa,SAAS;AAAA,QACnC;AAGD,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,qBAAqB;AACjCA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAED,cAAM,cAAc,MAAM,6BAA6B,cAAc,KAAK;AAC1EA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,aAAa,WAAW;AAGpC,YAAI,kBAAkB;AACtB,YAAI,kBAAkB,CAAE;AACxB,YAAI,aAAa;AAGjB,YAAI,eAAe,YAAY,SAAS;AAEvC,cAAI,YAAY,KAAK,eAAe,YAAY,KAAK,YAAY,SAAS,GAAG;AAC5E,kBAAM,YAAY,YAAY,KAAK,YACjC,OAAO,UAAQ,KAAK,aAAa,GAAG,EACpC,IAAI,UAAQ,GAAG,KAAK,IAAI,QAAQ,KAAK,MAAM,KAAK,aAAa,GAAG,CAAC,IAAI;AACvE,4BAAgB,KAAK,GAAG,SAAS;AACjC,yBAAa,KAAK,IAAI,YAAY,YAAY,KAAK,YAAY,CAAC,EAAE,aAAa,GAAG;AAAA,UAClF;AAGD,cAAI,YAAY,KAAK,qBAAqB,YAAY,KAAK,kBAAkB,SAAS,GAAG;AACxF,kBAAM,kBAAkB,YAAY,KAAK,kBACvC,OAAO,UAAQ,KAAK,aAAa,GAAG,EACpC,IAAI,UAAQ,GAAG,KAAK,IAAI,QAAQ,KAAK,MAAM,KAAK,aAAa,GAAG,CAAC,IAAI;AACvE,4BAAgB,KAAK,GAAG,eAAe;AACvC,yBAAa,KAAK,IAAI,YAAY,YAAY,KAAK,kBAAkB,CAAC,EAAE,aAAa,GAAG;AAAA,UACxF;AAGD,cAAI,gBAAgB,SAAS,GAAG;AAC/B,8BAAkB,aAAa,gBAAgB,KAAK,GAAG,CAAC;AAGxD,gBAAI,YAAY,KAAK,SAAS;AAC7B,iCAAmB;AAAA;AAAA,SAAc,YAAY,KAAK,OAAO;AAAA,YACzD;AAGD,gBAAI,YAAY,KAAK,eAAe,YAAY,KAAK,YAAY,SAAS,GAAG;AAC5E,iCAAmB;AAAA;AAAA,QAAa,YAAY,KAAK,YAAY,KAAK,GAAG,CAAC;AAAA,YACtE;AAAA,UACN,OAAW;AAENA,0BAAAA,oEAAa,gBAAgB;AAC7B,8BAAkB;AAClB,yBAAa;AAAA,UACb;AAAA,QACL,OAAU;AAENA,wBAAAA,MAAA,MAAA,QAAA,gDAAa,iBAAiB;AAC9B,cAAI,eAAe,YAAY,OAAO;AACrCA,0BAAA,MAAA,MAAA,SAAA,gDAAc,aAAa,YAAY,KAAK;AAAA,UAC5C;AACD,4BAAkB;AAClB,uBAAa;AAAA,QACb;AAGD,YAAI,aAAa,IAAI;AACpBA,wBAAA,MAAA,MAAA,QAAA,gDAAa,YAAY,UAAU,GAAG;AACtC,sBAAY,QAAQ;AAAA,QACpB;AAEDA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,SAAS,eAAe;AACpCA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,UAAU,UAAU;AAGhC,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,aAAa;AACzBA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAED,cAAM,WAAW;AAAA;AAAA,EAElB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyCd,cAAM,aAAa,MAAMC,iBAAe,gBAAC,QAAQ;AACjDD,sBAAAA,MAAA,MAAA,OAAA,gDAAY,WAAW,UAAU;AAGjC,YAAI;AACJ,YAAI;AAEH,gBAAM,kBAAkB,WAAW,QAAQ,sBAAsB,EAAE,EAAE,KAAM;AAC3E,yBAAe,KAAK,MAAM,eAAe;AAAA,QACzC,SAAQ,YAAY;AACpBA,wBAAAA,MAAA,MAAA,SAAA,gDAAc,aAAa,UAAU;AAErC,yBAAe;AAAA,YACd,UAAU,gBAAgB,QAAQ,cAAc,EAAE,KAAK;AAAA,YACvD,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,WAAW;AAAA,cACV,UAAU;AAAA,cACV,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACP;AAAA,YACD,cAAc;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,YACA;AAAA,YACD,kBAAkB;AAAA,cACjB,QAAQ,CAAC,iBAAiB;AAAA,cAC1B,UAAU,CAAC,eAAe;AAAA,cAC1B,aAAa,CAAC,oBAAoB;AAAA,YAClC;AAAA,UACD;AAAA,QACD;AAGD,uBAAe,QAAQ;AAGvB,cAAM,YAAY;AAAA,UACjB,IAAI,KAAK,IAAK;AAAA,UACd,OAAO,cAAc;AAAA,UACrB,UAAU,eAAe,MAAM;AAAA,UAC/B,SAAS,eAAe,MAAM;AAAA,UAC9B,WAAW,KAAK,IAAK;AAAA,UACrB,YAAY,eAAe,MAAM;AAAA,UACjC,WAAW,eAAe,MAAM;AAAA,UAChC,cAAc,eAAe,MAAM;AAAA,UACnC,kBAAkB,eAAe,MAAM;AAAA,UACvC,OAAM,oBAAI,QAAO,mBAAmB,SAAS;AAAA,YAC5C,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,UACb,CAAK;AAAA,QACD;AACD,0BAAkB,SAAS;AAE3BA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAAA,MAED,SAAQ,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,gDAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,YAAa;AAGjB,YAAI,aAAa;AACjB,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AAClD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,OAAU;AACN,uBAAa,QAAQ;AAAA,QACrB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACd,CAAI;AAAA,MACJ,UAAY;AACT,kBAAU,QAAQ;AAClB,oBAAY,QAAQ;AAAA,MACpB;AAAA,IACD;AAGD,UAAM,aAAa,CAAC,YAAY;AAC/B,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAGD,UAAM,mBAAmB,CAAC,YAAY;AACrC,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAmED,UAAM,qBAAqB,MAAM;AAChC,UAAI,CAAC,eAAe;AAAO;AAE3B,YAAM,SAAS;AAAA,QACd,IAAI,KAAK,IAAK;AAAA,QACd,OAAO,cAAc;AAAA,QACrB,UAAU,eAAe,MAAM;AAAA,QAC/B,YAAY,eAAe,MAAM;AAAA,QACjC,SAAS,eAAe,MAAM;AAAA,QAC9B,WAAW,eAAe,MAAM;AAAA,QAChC,cAAc,eAAe,MAAM;AAAA,QACnC,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,MACnC;AAED,YAAM,kBAAkBA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AACvE,sBAAgB,QAAQ,MAAM;AAC9BA,0BAAI,eAAe,uBAAuB,eAAe;AAEzD,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACT,CAAG;AAAA,IACD;AAED,UAAM,qBAAqB,MAAM;AAChC,YAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,qBAAe,QAAQ;AAAA,IACvB;AAED,UAAM,oBAAoB,CAAC,WAAW;AACrC,UAAI;AACH,cAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,gBAAQ,QAAQ,MAAM;AAEtB,YAAI,QAAQ,SAAS,IAAI;AACxB,kBAAQ,OAAO,EAAE;AAAA,QACjB;AACDA,4BAAI,eAAe,uBAAuB,OAAO;AACjD,uBAAe,QAAQ;AAAA,MACvB,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,aAAa,KAAK;AAAA,MAChC;AAAA,IACD;AAED,UAAM,aAAa,CAAC,cAAc;AACjC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK,QAAS;AAC1B,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,aAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,OAAO;AAAA,IAC3C;AAED,UAAM,oBAAoB,CAAC,WAAW;AAErC,qBAAe,QAAQ;AACvB,oBAAc,QAAQ,OAAO;AAAA,IAC7B;AAED,UAAM,iBAAiB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACR,CAAG;AAAA,IACD;AAEDE,kBAAAA,UAAU,MAAM;AACf,yBAAoB;AAAA,IACtB,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3pBF,GAAG,WAAW,eAAe;"}
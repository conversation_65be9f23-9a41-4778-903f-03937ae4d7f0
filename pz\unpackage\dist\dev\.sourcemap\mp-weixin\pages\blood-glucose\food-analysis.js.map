{"version": 3, "file": "food-analysis.js", "sources": ["pages/blood-glucose/food-analysis.vue", "D:/bc Files/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYmxvb2QtZ2x1Y29zZS9mb29kLWFuYWx5c2lzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"page-container\">\r\n\t\t<navbar :isHomePage=\"false\" title=\"饮食分析\" />\r\n\r\n\t\t<!-- 主体内容 -->\r\n\t\t<view class=\"content-container\" :style=\"contentStyle\">\r\n\r\n\t\t\t<!-- 上传区域 -->\r\n\t\t\t<view class=\"upload-section\">\r\n\t\t\t\t<view class=\"upload-header\">\r\n\t\t\t\t\t<text class=\"upload-title\">AI食物识别</text>\r\n\t\t\t\t\t<text class=\"upload-subtitle\">拍照或选择图片，AI将分析食物营养成分</text>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<view class=\"upload-area\" @tap=\"chooseImage\">\r\n\t\t\t\t\t<image v-if=\"selectedImage\" :src=\"selectedImage\" class=\"preview-image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view v-else class=\"upload-placeholder\">\r\n\t\t\t\t\t\t<image src=\"../../static/resource/images/camera.png\" class=\"upload-icon\"></image>\r\n\t\t\t\t\t\t<text class=\"upload-text\">点击上传食物图片</text>\r\n\t\t\t\t\t\t<text class=\"upload-hint\">支持JPG、PNG格式</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"upload-actions\" v-if=\"selectedImage\">\r\n\t\t\t\t\t<button class=\"action-btn secondary\" @tap=\"reSelectImage\">重新选择</button>\r\n\t\t\t\t\t<button class=\"action-btn primary\" @tap=\"analyzeFood\" :disabled=\"analyzing\">\r\n\t\t\t\t\t\t{{ analyzing ? '分析中...' : '开始分析' }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 分析状态提示 -->\r\n\t\t\t\t<view class=\"analysis-status\" v-if=\"analyzing || errorMessage\">\r\n\t\t\t\t\t<view class=\"status-item\" v-if=\"analyzing\">\r\n\t\t\t\t\t\t<view class=\"loading-icon\"></view>\r\n\t\t\t\t\t\t<text class=\"status-text\">{{ currentStep }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"error-item\" v-if=\"errorMessage\">\r\n\t\t\t\t\t\t<text class=\"error-icon\">⚠️</text>\r\n\t\t\t\t\t\t<text class=\"error-text\">{{ errorMessage }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 分析结果 -->\r\n\t\t\t<view class=\"analysis-result\" v-if=\"analysisResult\">\r\n\t\t\t\t<view class=\"result-header\">\r\n\t\t\t\t\t<text class=\"result-title\">分析结果</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 识别的食物 -->\r\n\t\t\t\t<view class=\"food-info\">\r\n\t\t\t\t\t<view class=\"food-name\">\r\n\t\t\t\t\t\t<text class=\"name-text\">{{ analysisResult.foodName }}</text>\r\n\t\t\t\t\t\t<view class=\"confidence-badge\">\r\n\t\t\t\t\t\t\t<text class=\"confidence-text\">置信度: {{ analysisResult.confidence }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 营养成分 -->\r\n\t\t\t\t<view class=\"nutrition-section\">\r\n\t\t\t\t\t<view class=\"section-title\">营养成分 (每100g)</view>\r\n\t\t\t\t\t<view class=\"nutrition-grid\">\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">热量</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.calories }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">kcal</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">碳水化合物</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.carbs }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">蛋白质</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.protein }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">脂肪</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fat }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">膳食纤维</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fiber }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nutrition-item\">\r\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">糖分</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.sugar }}</text>\r\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- GI值提示 -->\r\n\t\t\t\t<view class=\"gi-section\">\r\n\t\t\t\t\t<view class=\"gi-header\">\r\n\t\t\t\t\t\t<text class=\"gi-title\">GI值分析</text>\r\n\t\t\t\t\t\t<view class=\"gi-badge\" :class=\"getGIClass(analysisResult.giValue)\">\r\n\t\t\t\t\t\t\t<text class=\"gi-value\">GI: {{ analysisResult.giValue }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gi-description\">\r\n\t\t\t\t\t\t<text class=\"gi-text\">{{ getGIDescription(analysisResult.giValue) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 详细分析 -->\r\n\t\t\t\t<view class=\"detailed-analysis\" v-if=\"analysisResult.detailedAnalysis\">\r\n\r\n\t\t\t\t\t<!-- 健康建议 -->\r\n\t\t\t\t\t<view class=\"advice-section\">\r\n\t\t\t\t\t\t<view class=\"advice-header\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/resource/images/tips.png\" class=\"advice-icon\"></image>\r\n\t\t\t\t\t\t\t<text class=\"advice-title\">健康建议</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"advice-content-unified\">\r\n\t\t\t\t\t\t\t<text class=\"advice-text-unified\">\r\n\t\t\t\t\t\t\t\t{{ analysisResult.healthAdvice.join('；') }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 推荐食用 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header can-eat\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">✅ 推荐食用</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.canEat.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 需要控制 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header limit-eat\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">⚠️ 需要控制</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.limitEat.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 改进建议 -->\r\n\t\t\t\t\t<view class=\"analysis-section\">\r\n\t\t\t\t\t\t<view class=\"section-header suggestions\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">💡 改进建议</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"unified-content\">\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\tclass=\"unified-text\">{{ analysisResult.detailedAnalysis.suggestions.join('；') }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<!-- 保存记录 -->\r\n\t\t\t\t<view class=\"save-section\">\r\n\t\t\t\t\t<button class=\"save-btn\" @tap=\"saveAnalysisRecord\">保存到饮食记录</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 历史记录 -->\r\n\t\t\t<view class=\"history-section\" v-if=\"historyRecords.length > 0\">\r\n\t\t\t\t<view class=\"history-header\">\r\n\t\t\t\t\t<text class=\"history-title\">最近分析</text>\r\n\t\t\t\t\t<text class=\"view-all\" @tap=\"viewAllHistory\">查看全部</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"history-list\">\r\n\t\t\t\t\t<view class=\"history-item\" v-for=\"record in historyRecords.slice(0, 3)\" :key=\"record.id\"\r\n\t\t\t\t\t\t@tap=\"viewHistoryDetail(record)\">\r\n\t\t\t\t\t\t<image :src=\"record.image\" class=\"history-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"history-info\">\r\n\t\t\t\t\t\t\t<text class=\"history-food-name\">{{ record.foodName }}</text>\r\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ formatDate(record.timestamp) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"history-gi\" :class=\"getGIClass(record.giValue)\">\r\n\t\t\t\t\t\t\t<text class=\"history-gi-text\">GI: {{ record.giValue }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\n\timport {\r\n\t\tref,\r\n\t\tcomputed,\r\n\t\tonMounted\r\n\t} from 'vue'\r\n\timport navbar from '../../components/navbar/navbar.vue'\r\n\timport {\r\n\t\tsendMessageToAI\r\n\t} from '../../utils/ai/service'\r\n\timport {\r\n\t\tanalyzeImageWithQwenVL\r\n\t} from '../../utils/ai/qwenVLService'\r\n\r\n\r\n\r\n\r\n\t// 响应式数据\r\n\tconst selectedImage = ref('')\r\n\tconst analyzing = ref(false)\r\n\tconst analysisResult = ref(null)\r\n\tconst historyRecords = ref([])\r\n\tconst currentStep = ref('') // 当前处理步骤\r\n\tconst errorMessage = ref('') // 错误信息\r\n\tconst qwenRecognitionResult = ref('') // 通义千问识别结果\r\n\r\n\t// 计算属性\r\n\tconst contentStyle = computed(() => {\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n\t\tconst navHeight = menuButtonInfo.top + menuButtonInfo.height + 8\r\n\t\treturn {\r\n\t\t\tpaddingTop: navHeight + 'px'\r\n\t\t}\r\n\t})\r\n\r\n\t// 方法\r\n\tconst chooseImage = () => {\r\n\t\tuni.chooseImage({\r\n\t\t\tcount: 1,\r\n\t\t\tsizeType: ['compressed'],\r\n\t\t\tsourceType: ['camera', 'album'],\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tselectedImage.value = res.tempFilePaths[0]\r\n\t\t\t\tanalysisResult.value = null\r\n\t\t\t\tconsole.log('图片选择成功:', res.tempFilePaths[0])\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tconsole.error('选择图片失败:', err)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '选择图片失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\r\n\tconst reSelectImage = () => {\r\n\t\tselectedImage.value = ''\r\n\t\tanalysisResult.value = null\r\n\t}\r\n\r\n\t/**\r\n\t * AI分析食物营养成分\r\n\t */\r\n\tconst analyzeFood = async () => {\r\n\t\tif (!selectedImage.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请先选择图片',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tanalyzing.value = true\r\n\r\n\t\ttry {\r\n\t\t\terrorMessage.value = ''\r\n\r\n\t\t\t// 图片质量预检查\r\n\t\t\tcurrentStep.value = '正在检查图片质量...'\r\n\t\t\tconsole.log('开始图片质量检查...')\r\n\r\n\t\t\t// 检查图片文件大小（简单的质量指标）\r\n\t\t\ttry {\r\n\t\t\t\tconst fileInfo = await new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.getFileInfo({\r\n\t\t\t\t\t\tfilePath: selectedImage.value,\r\n\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\r\n\t\t\t\tconsole.log('图片文件信息:', fileInfo)\r\n\r\n\t\t\t\t// 检查文件大小（太小可能质量不佳，太大可能处理困难）\r\n\t\t\t\tif (fileInfo.size < 10000) { // 小于10KB\r\n\t\t\t\t\tconsole.warn('图片文件过小，可能质量不佳')\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '图片质量可能不佳，建议重新拍摄',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (fileInfo.size > 10000000) { // 大于10MB\r\n\t\t\t\t\tconsole.warn('图片文件过大')\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '图片文件较大，处理可能较慢',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t} catch (fileError) {\r\n\t\t\t\tconsole.warn('🔍 [食物识别] 无法获取文件信息:', fileError)\r\n\t\t\t}\r\n\r\n\t\t\t// 第一步：使用通义千问VL-MAX进行图像识别\r\n\t\t\tcurrentStep.value = '正在识别食物...'\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 第一步：通义千问VL-MAX图像识别 =====')\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在识别食物...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\r\n\t\t\tconst qwenDescription = await analyzeImageWithQwenVL(selectedImage.value)\r\n\t\t\tconsole.log('🔍 [食物识别] 通义千问VL-MAX识别结果:')\r\n\t\t\tconsole.log(qwenDescription)\r\n\t\t\tconsole.log('🔍 [食物识别] 识别结果类型:', typeof qwenDescription)\r\n\t\t\tconsole.log('🔍 [食物识别] 识别结果长度:', qwenDescription ? qwenDescription.length : 0)\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 通义千问VL-MAX识别完成 =====')\r\n\r\n\t\t\t// 验证识别结果是否有效\r\n\t\t\tif (!qwenDescription || qwenDescription.trim().length === 0) {\r\n\t\t\t\tthrow new Error('通义千问VL-MAX识别失败：返回空结果')\r\n\t\t\t}\r\n\r\n\t\t\t// 保存识别结果到响应式变量，供前端显示\r\n\t\t\tqwenRecognitionResult.value = qwenDescription\r\n\r\n\t\t\t// 第二步：将通义千问的描述发送给DeepSeek进行结构化处理\r\n\t\t\tcurrentStep.value = '正在分析营养成分...'\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 第二步：DeepSeek结构化处理 =====')\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在分析营养成分...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\r\n\t\t\t// 构建更详细的prompt，确保DeepSeek理解任务\r\n\t\t\tconst deepSeekPrompt = `你是一位专业的营养师和糖尿病饮食专家。请仔细分析以下通义千问VL-MAX提供的详细食物图像描述，并基于这个具体的食物描述进行营养分析。\r\n\r\n=== 食物图像识别结果 ===\r\n${qwenDescription}\r\n=== 识别结果结束 ===\r\n\r\n请基于上述具体的食物识别结果，进行专业的营养成分分析和糖尿病饮食建议。请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：\r\n\r\n{\r\n  \"foodName\": \"根据识别结果确定的主要食物名称\",\r\n  \"confidence\": \"根据识别清晰度确定的置信度数值(0-100)\",\r\n  \"giValue\": \"根据具体食物确定的血糖生成指数(0-100)\",\r\n  \"nutrition\": {\r\n    \"calories\": \"根据食物类型和份量确定的卡路里数值\",\r\n    \"carbs\": \"根据食物确定的碳水化合物含量(克)\",\r\n    \"protein\": \"根据食物确定的蛋白质含量(克)\",\r\n    \"fat\": \"根据食物确定的脂肪含量(克)\",\r\n    \"fiber\": \"根据食物确定的纤维含量(克)\",\r\n    \"sugar\": \"根据食物确定的糖分含量(克)\"\r\n  },\r\n  \"healthAdvice\": [\r\n    \"基于具体食物特点的健康建议1\",\r\n    \"基于具体食物特点的健康建议2\",\r\n    \"基于具体食物特点的健康建议3\"\r\n  ],\r\n  \"detailedAnalysis\": {\r\n    \"canEat\": [\r\n      \"基于识别食物推荐食用的内容及原因\"\r\n    ],\r\n    \"limitEat\": [\r\n      \"基于识别食物需要限制的内容及原因\"\r\n    ],\r\n    \"suggestions\": [\r\n      \"基于具体食物的个性化饮食建议\"\r\n    ]\r\n  }\r\n}\r\n\r\n分析要求：\r\n1. 必须基于上述具体的食物识别结果进行分析，不要使用通用数据\r\n2. 所有数值必须是合理的营养数据，符合实际食物特征\r\n3. GI值范围在0-100之间，要准确反映食物的血糖影响\r\n4. 健康建议要针对糖尿病患者，结合具体食物特点\r\n5. foodName要与识别结果中的主要食物一致\r\n6. 分析要专业且实用，避免泛泛而谈\r\n7. 返回纯JSON格式，不要markdown代码块或其他格式`\r\n\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 数据传递验证 =====')\r\n\t\t\tconsole.log('🔍 [食物识别] qwenDescription变量值:')\r\n\t\t\tconsole.log(qwenDescription)\r\n\t\t\tconsole.log('🔍 [食物识别] prompt中是否包含识别结果:', deepSeekPrompt.includes(qwenDescription))\r\n\t\t\tconsole.log('🔍 [食物识别] 完整的DeepSeek请求内容:')\r\n\t\t\tconsole.log(deepSeekPrompt)\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 发送请求到DeepSeek =====')\r\n\r\n\t\t\tconst deepSeekResponse = await sendMessageToAI(deepSeekPrompt)\r\n\t\t\tconsole.log('🔍 [食物识别] ===== DeepSeek响应接收 =====')\r\n\t\t\tconsole.log('🔍 [食物识别] DeepSeek返回的原始结果:')\r\n\t\t\tconsole.log(deepSeekResponse)\r\n\t\t\tconsole.log('🔍 [食物识别] 响应类型:', typeof deepSeekResponse)\r\n\t\t\tconsole.log('🔍 [食物识别] 响应长度:', deepSeekResponse ? deepSeekResponse.length : 0)\r\n\t\t\tconsole.log('🔍 [食物识别] ===== DeepSeek处理完成 =====')\r\n\r\n\t\t\t// 第三步：解析DeepSeek返回的JSON结果\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 第三步：JSON解析处理 =====')\r\n\t\t\tlet parsedResult\r\n\t\t\ttry {\r\n\t\t\t\t// 清理可能的markdown格式\r\n\t\t\t\tconst cleanedResponse = deepSeekResponse.replace(/```json\\s*|\\s*```/g, '').trim()\r\n\t\t\t\tconsole.log('🔍 [食物识别] 清理后的响应内容:')\r\n\t\t\t\tconsole.log(cleanedResponse)\r\n\t\t\t\tconsole.log('🔍 [食物识别] 开始JSON解析...')\r\n\r\n\t\t\t\tparsedResult = JSON.parse(cleanedResponse)\r\n\t\t\t\tconsole.log('🔍 [食物识别] ===== JSON解析成功 =====')\r\n\t\t\t\tconsole.log('🔍 [食物识别] 解析后的结果:')\r\n\t\t\t\tconsole.log(parsedResult)\r\n\r\n\t\t\t\t// 验证解析结果的关键字段\r\n\t\t\t\tconsole.log('🔍 [食物识别] 结果验证:')\r\n\t\t\t\tconsole.log('🔍 [食物识别] - foodName:', parsedResult.foodName)\r\n\t\t\t\tconsole.log('🔍 [食物识别] - confidence:', parsedResult.confidence)\r\n\t\t\t\tconsole.log('🔍 [食物识别] - giValue:', parsedResult.giValue)\r\n\t\t\t\tconsole.log('🔍 [食物识别] - nutrition:', parsedResult.nutrition)\r\n\r\n\t\t\t} catch (parseError) {\r\n\t\t\t\tconsole.error('🔍 [食物识别] ===== JSON解析失败 =====')\r\n\t\t\t\tconsole.error('🔍 [食物识别] 解析错误:', parseError)\r\n\t\t\t\tconsole.error('🔍 [食物识别] 错误详情:', parseError.message)\r\n\t\t\t\tconsole.error('🔍 [食物识别] 原始响应内容:', deepSeekResponse)\r\n\t\t\t\tconsole.error('🔍 [食物识别] 清理后内容:', deepSeekResponse.replace(/```json\\s*|\\s*```/g, '').trim())\r\n\t\t\t\t// 使用备用数据结构，尝试基于通义千问的识别结果\r\n\t\t\t\tconsole.log('🔍 [食物识别] 生成备用数据结构...')\r\n\r\n\t\t\t\t// 尝试从通义千问结果中提取食物名称\r\n\t\t\t\tlet extractedFoodName = \"识别的食物\"\r\n\t\t\t\tif (qwenDescription && qwenDescription.length > 0) {\r\n\t\t\t\t\t// 简单的食物名称提取逻辑\r\n\t\t\t\t\tconst foodKeywords = ['米饭', '面条', '苹果', '香蕉', '鸡蛋', '牛奶', '面包', '蔬菜', '肉类', '鱼类']\r\n\t\t\t\t\tfor (const keyword of foodKeywords) {\r\n\t\t\t\t\t\tif (qwenDescription.includes(keyword)) {\r\n\t\t\t\t\t\t\textractedFoodName = keyword\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tparsedResult = {\r\n\t\t\t\t\tfoodName: extractedFoodName,\r\n\t\t\t\t\tconfidence: 75, // 降低置信度，因为是备用数据\r\n\t\t\t\t\tgiValue: 60,\r\n\t\t\t\t\tnutrition: {\r\n\t\t\t\t\t\tcalories: 150,\r\n\t\t\t\t\t\tcarbs: 30,\r\n\t\t\t\t\t\tprotein: 6,\r\n\t\t\t\t\t\tfat: 5,\r\n\t\t\t\t\t\tfiber: 3,\r\n\t\t\t\t\t\tsugar: 8\r\n\t\t\t\t\t},\r\n\t\t\t\t\thealthAdvice: [\r\n\t\t\t\t\t\t`基于图像识别，${extractedFoodName}需要注意控制食用份量`,\r\n\t\t\t\t\t\t\"建议搭配低GI食物一起食用\",\r\n\t\t\t\t\t\t\"餐后适当运动有助于血糖控制\"\r\n\t\t\t\t\t],\r\n\t\t\t\t\tdetailedAnalysis: {\r\n\t\t\t\t\t\tcanEat: [`根据识别结果，${extractedFoodName}适量食用有益健康`],\r\n\t\t\t\t\t\tlimitEat: [\"注意控制高糖高脂食物的摄入\"],\r\n\t\t\t\t\t\tsuggestions: [\"由于AI分析服务暂时不可用，建议咨询专业营养师获取准确的饮食建议\"]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log('🔍 [食物识别] 备用数据结构生成完成:', parsedResult)\r\n\t\t\t\tconsole.log('🔍 [食物识别] 提取的食物名称:', extractedFoodName)\r\n\t\t\t}\r\n\r\n\t\t\t// 设置分析结果\r\n\t\t\tconsole.log('🔍 [食物识别] ===== 设置最终分析结果 =====')\r\n\t\t\tconsole.log('🔍 [食物识别] 最终结果数据:')\r\n\t\t\tconsole.log(parsedResult)\r\n\r\n\t\t\t// 验证结果的完整性\r\n\t\t\tconst requiredFields = ['foodName', 'confidence', 'giValue', 'nutrition', 'healthAdvice', 'detailedAnalysis']\r\n\t\t\tconst missingFields = requiredFields.filter(field => !parsedResult[field])\r\n\t\t\tif (missingFields.length > 0) {\r\n\t\t\t\tconsole.warn('🔍 [食物识别] 警告：结果缺少必要字段:', missingFields)\r\n\t\t\t}\r\n\r\n\t\t\tanalysisResult.value = parsedResult\r\n\t\t\tconsole.log('🔍 [食物识别] 分析结果已设置到响应式变量')\r\n\t\t\tconsole.log('🔍 [食物识别] analysisResult.value:', analysisResult.value)\r\n\r\n\t\t\t// 保存到历史记录\r\n\t\t\tconst newRecord = {\r\n\t\t\t\tid: Date.now(),\r\n\t\t\t\timage: selectedImage.value,\r\n\t\t\t\tfoodName: analysisResult.value.foodName,\r\n\t\t\t\tgiValue: analysisResult.value.giValue,\r\n\t\t\t\ttimestamp: Date.now(),\r\n\t\t\t\tconfidence: analysisResult.value.confidence,\r\n\t\t\t\tnutrition: analysisResult.value.nutrition,\r\n\t\t\t\thealthAdvice: analysisResult.value.healthAdvice,\r\n\t\t\t\tdetailedAnalysis: analysisResult.value.detailedAnalysis,\r\n\t\t\t\tdate: new Date().toLocaleDateString('zh-CN', {\r\n\t\t\t\t\tmonth: 'numeric',\r\n\t\t\t\t\tday: 'numeric',\r\n\t\t\t\t\thour: '2-digit',\r\n\t\t\t\t\tminute: '2-digit'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tsaveHistoryRecord(newRecord)\r\n\r\n\t\t\tuni.hideLoading()\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分析完成',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('🔍 [食物识别] 分析失败:', error)\r\n\t\t\tconsole.error('🔍 [食物识别] 错误详情:', {\r\n\t\t\t\tmessage: error.message,\r\n\t\t\t\tstack: error.stack,\r\n\t\t\t\tname: error.name\r\n\t\t\t})\r\n\t\t\tuni.hideLoading()\r\n\r\n\t\t\t// 根据错误类型提供不同的提示\r\n\t\t\tlet errorTitle = '分析失败，请重试'\r\n\t\t\tif (error.message && error.message.includes('网络')) {\r\n\t\t\t\terrorTitle = '网络连接失败，请检查网络'\r\n\t\t\t\terrorMessage.value = '请检查网络连接后重试'\r\n\t\t\t\tconsole.error('🔍 [食物识别] 网络错误:', error.message)\r\n\t\t\t} else if (error.message && error.message.includes('通义千问')) {\r\n\t\t\t\terrorTitle = '图片识别失败'\r\n\t\t\t\terrorMessage.value = '请选择清晰的食物图片重试'\r\n\t\t\t\tconsole.error('🔍 [食物识别] 通义千问VL-MAX错误:', error.message)\r\n\t\t\t} else if (error.message && error.message.includes('AI服务')) {\r\n\t\t\t\terrorTitle = 'AI分析服务暂时不可用'\r\n\t\t\t\terrorMessage.value = '服务暂时繁忙，请稍后重试'\r\n\t\t\t\tconsole.error('🔍 [食物识别] DeepSeek API错误:', error.message)\r\n\t\t\t} else {\r\n\t\t\t\terrorMessage.value = '分析过程中出现错误，请重新尝试'\r\n\t\t\t\tconsole.error('🔍 [食物识别] 未知错误:', error.message)\r\n\t\t\t}\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: errorTitle,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t})\r\n\t\t} finally {\r\n\t\t\tanalyzing.value = false\r\n\t\t\tcurrentStep.value = ''\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取GI值等级样式类\r\n\tconst getGIClass = (giValue) => {\r\n\t\tif (giValue <= 55) return 'low'\r\n\t\tif (giValue <= 70) return 'medium'\r\n\t\treturn 'high'\r\n\t}\r\n\r\n\t// 获取GI值描述\r\n\tconst getGIDescription = (giValue) => {\r\n\t\tif (giValue <= 55) return '低GI食物，对血糖影响较小，适合糖尿病患者食用'\r\n\t\tif (giValue <= 70) return '中等GI食物，适量食用，建议搭配低GI食物'\r\n\t\treturn '高GI食物，会快速升高血糖，糖尿病患者需严格控制'\r\n\t}\r\n\r\n\t// 备用分析结果生成\r\n\tconst generateBackupAnalysis = (foodName) => {\r\n\t\tconst foodDatabase = {\r\n\t\t\t'白米饭': {\r\n\t\t\t\tfoodName: '白米饭',\r\n\t\t\t\tconfidence: 95,\r\n\t\t\t\tgiValue: 83,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 130,\r\n\t\t\t\t\tcarbs: 28,\r\n\t\t\t\t\tprotein: 2.7,\r\n\t\t\t\t\tfat: 0.3,\r\n\t\t\t\t\tfiber: 0.4,\r\n\t\t\t\t\tsugar: 0.1\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['属于高GI食物，会快速升高血糖', '建议搭配蔬菜和蛋白质一起食用', '可以选择糙米或杂粮饭作为替代']\r\n\t\t\t},\r\n\t\t\t'苹果': {\r\n\t\t\t\tfoodName: '苹果',\r\n\t\t\t\tconfidence: 92,\r\n\t\t\t\tgiValue: 36,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 52,\r\n\t\t\t\t\tcarbs: 14,\r\n\t\t\t\t\tprotein: 0.3,\r\n\t\t\t\t\tfat: 0.2,\r\n\t\t\t\t\tfiber: 2.4,\r\n\t\t\t\t\tsugar: 10\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['属于低GI食物，对血糖影响较小', '富含膳食纤维，有助于血糖稳定', '建议在两餐之间食用，避免空腹']\r\n\t\t\t},\r\n\t\t\t'糙米饭': {\r\n\t\t\t\tfoodName: '糙米饭',\r\n\t\t\t\tconfidence: 90,\r\n\t\t\t\tgiValue: 50,\r\n\t\t\t\tnutrition: {\r\n\t\t\t\t\tcalories: 112,\r\n\t\t\t\t\tcarbs: 23,\r\n\t\t\t\t\tprotein: 2.6,\r\n\t\t\t\t\tfat: 0.9,\r\n\t\t\t\t\tfiber: 1.8,\r\n\t\t\t\t\tsugar: 0.4\r\n\t\t\t\t},\r\n\t\t\t\thealthAdvice: ['中等GI食物，比白米饭更适合糖尿病患者', '富含膳食纤维，有助于血糖稳定', '建议控制食用量，搭配蔬菜食用']\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn foodDatabase[foodName] || {\r\n\t\t\tfoodName: foodName,\r\n\t\t\tconfidence: 85,\r\n\t\t\tgiValue: 55,\r\n\t\t\tnutrition: {\r\n\t\t\t\tcalories: 100,\r\n\t\t\t\tcarbs: 20,\r\n\t\t\t\tprotein: 3,\r\n\t\t\t\tfat: 1,\r\n\t\t\t\tfiber: 2,\r\n\t\t\t\tsugar: 5\r\n\t\t\t},\r\n\t\t\thealthAdvice: ['请咨询营养师获取准确信息', '建议适量食用', '注意监测血糖变化']\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\tconst saveAnalysisRecord = () => {\r\n\t\tif (!analysisResult.value) return\r\n\r\n\t\tconst record = {\r\n\t\t\tid: Date.now(),\r\n\t\t\timage: selectedImage.value,\r\n\t\t\tfoodName: analysisResult.value.foodName,\r\n\t\t\tconfidence: analysisResult.value.confidence,\r\n\t\t\tgiValue: analysisResult.value.giValue,\r\n\t\t\tnutrition: analysisResult.value.nutrition,\r\n\t\t\thealthAdvice: analysisResult.value.healthAdvice,\r\n\t\t\ttimestamp: new Date().toISOString()\r\n\t\t}\r\n\r\n\t\tconst existingRecords = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\texistingRecords.unshift(record)\r\n\t\tuni.setStorageSync('foodAnalysisRecords', existingRecords)\r\n\r\n\t\thistoryRecords.value = existingRecords\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '保存成功',\r\n\t\t\ticon: 'success'\r\n\t\t})\r\n\t}\r\n\r\n\tconst loadHistoryRecords = () => {\r\n\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\thistoryRecords.value = records\r\n\t}\r\n\r\n\tconst saveHistoryRecord = (record) => {\r\n\t\ttry {\r\n\t\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\r\n\t\t\trecords.unshift(record)\r\n\t\t\t// 只保留最近50条记录\r\n\t\t\tif (records.length > 50) {\r\n\t\t\t\trecords.splice(50)\r\n\t\t\t}\r\n\t\t\tuni.setStorageSync('foodAnalysisRecords', records)\r\n\t\t\thistoryRecords.value = records\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('保存历史记录失败:', error)\r\n\t\t}\r\n\t}\r\n\r\n\tconst formatDate = (timestamp) => {\r\n\t\tconst date = new Date(timestamp)\r\n\t\tconst month = date.getMonth() + 1\r\n\t\tconst day = date.getDate()\r\n\t\tconst hours = date.getHours().toString().padStart(2, '0')\r\n\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\r\n\t\treturn `${month}月${day}日 ${hours}:${minutes}`\r\n\t}\r\n\r\n\tconst viewHistoryDetail = (record) => {\r\n\t\t// 显示历史记录详情\r\n\t\tanalysisResult.value = record\r\n\t\tselectedImage.value = record.image\r\n\t}\r\n\r\n\tconst viewAllHistory = () => {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/blood-glucose/food-history'\r\n\t\t})\r\n\t}\r\n\r\n\tonMounted(() => {\r\n\t\tloadHistoryRecords()\r\n\t})\r\n</script>\r\n\r\n<style scoped>\r\n\t.page-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f7fa;\r\n\t}\r\n\r\n\t.content-container {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t/* 上传区域 */\r\n\t.upload-section {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.upload-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.upload-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.upload-subtitle {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.upload-area {\r\n\t\tborder: 3rpx dashed #e0e0e0;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmin-height: 400rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\r\n\t.upload-area:active {\r\n\t\tborder-color: #00b38a;\r\n\t\tbackground: rgba(0, 179, 138, 0.05);\r\n\t}\r\n\r\n\t.preview-image {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.upload-placeholder {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.upload-icon {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.upload-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.upload-hint {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.upload-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\tflex: 1;\r\n\t\tpadding: 24rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.action-btn.secondary {\r\n\t\tbackground: #f5f7fa;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.action-btn.primary {\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn:disabled {\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t/* 分析结果 */\r\n\t.analysis-result {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.result-header {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t/* 食物信息 */\r\n\t.food-info {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.food-name {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.name-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.confidence-badge {\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.confidence-text {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t/* 营养成分 */\r\n\t.nutrition-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.nutrition-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: 1fr 1fr;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.nutrition-item {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.nutrition-label {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.nutrition-value {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #00b38a;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\r\n\t.nutrition-unit {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t/* GI值分析 */\r\n\t.gi-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.gi-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.gi-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.gi-badge {\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.gi-badge.low {\r\n\t\tbackground: #4CAF50;\r\n\t}\r\n\r\n\t.gi-badge.medium {\r\n\t\tbackground: #ff9800;\r\n\t}\r\n\r\n\t.gi-badge.high {\r\n\t\tbackground: #f44336;\r\n\t}\r\n\r\n\t.gi-value {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.gi-description {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f0f8ff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.gi-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t/* 健康建议 */\r\n\t.advice-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.advice-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.advice-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\r\n\t.advice-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.advice-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.advice-text {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #fff3cd;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #ffc107;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #856404;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.advice-content-unified {\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.advice-text-unified {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.8;\r\n\t\ttext-align: justify;\r\n\t}\r\n\r\n\t/* 详细分析 */\r\n\t.detailed-analysis {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.analysis-section {\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.section-header {\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.section-header.can-eat {\r\n\t\tbackground: rgba(76, 175, 80, 0.1);\r\n\t\tborder-left: 6rpx solid #4CAF50;\r\n\t}\r\n\r\n\t.section-header.limit-eat {\r\n\t\tbackground: rgba(255, 152, 0, 0.1);\r\n\t\tborder-left: 6rpx solid #ff9800;\r\n\t}\r\n\r\n\t.section-header.suggestions {\r\n\t\tbackground: rgba(0, 179, 138, 0.1);\r\n\t\tborder-left: 6rpx solid #00b38a;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.food-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.food-item {\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder-left: 4rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.food-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.unified-content {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.unified-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #555;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: justify;\r\n\t}\r\n\r\n\t/* 保存按钮 */\r\n\t.save-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.save-btn {\r\n\t\twidth: 100%;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: #00b38a;\r\n\t\tcolor: white;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t/* 历史记录 */\r\n\t.history-section {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.history-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.history-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.view-all {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #00b38a;\r\n\t}\r\n\r\n\t.history-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.history-image {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.history-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.history-food-name {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.history-date {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.history-gi {\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.history-gi.low {\r\n\t\tbackground: #4CAF50;\r\n\t}\r\n\r\n\t.history-gi.medium {\r\n\t\tbackground: #ff9800;\r\n\t}\r\n\r\n\t.history-gi.high {\r\n\t\tbackground: #f44336;\r\n\t}\r\n\r\n\t.history-gi-text {\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\t/* 分析状态样式 */\r\n\t.analysis-status {\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.status-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\r\n\t.loading-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tborder: 3rpx solid #e0e0e0;\r\n\t\tborder-top: 3rpx solid #3b82f6;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% { transform: rotate(0deg); }\r\n\t\t100% { transform: rotate(360deg); }\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #3b82f6;\r\n\t}\r\n\r\n\t.error-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 12rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.error-icon {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.error-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #f44336;\r\n\t\tflex: 1;\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/2025077166-源代码/pz/pages/blood-glucose/food-analysis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "analyzeImageWithQwenVL", "sendMessageToAI", "onMounted"], "mappings": ";;;;;;;;AAmMC,MAAM,SAAS,MAAW;;;;AAY1B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAC/B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,wBAAwBA,cAAG,IAAC,EAAE;AAGpC,UAAM,eAAeC,cAAAA,SAAS,MAAM;AAChBC,oBAAAA,MAAI,kBAAmB;AAC1C,YAAM,iBAAiBA,cAAG,MAAC,gCAAiC;AAC5D,YAAM,YAAY,eAAe,MAAM,eAAe,SAAS;AAC/D,aAAO;AAAA,QACN,YAAY,YAAY;AAAA,MACxB;AAAA,IACH,CAAE;AAGD,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,wBAAc,QAAQ,IAAI,cAAc,CAAC;AACzC,yBAAe,QAAQ;AACvBA,8BAAY,MAAA,OAAA,gDAAA,WAAW,IAAI,cAAc,CAAC,CAAC;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAAA,qEAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AAAA,QACD;AAAA,MACJ,CAAG;AAAA,IACD;AAED,UAAM,gBAAgB,MAAM;AAC3B,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AAAA,IACvB;AAKD,UAAM,cAAc,YAAY;AAC/B,UAAI,CAAC,cAAc,OAAO;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD;AAAA,MACA;AAED,gBAAU,QAAQ;AAElB,UAAI;AACH,qBAAa,QAAQ;AAGrB,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,aAAa;AAGzB,YAAI;AACH,gBAAM,WAAW,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvDA,0BAAAA,MAAI,YAAY;AAAA,cACf,UAAU,cAAc;AAAA,cACxB,SAAS;AAAA,cACT,MAAM;AAAA,YACZ,CAAM;AAAA,UACN,CAAK;AAEDA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,WAAW,QAAQ;AAG/B,cAAI,SAAS,OAAO,KAAO;AAC1BA,0BAAAA,oEAAa,eAAe;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAChB,CAAM;AAAA,UACN,WAAe,SAAS,OAAO,KAAU;AACpCA,0BAAAA,oEAAa,QAAQ;AACrBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAChB,CAAM;AAAA,UACD;AAAA,QACD,SAAQ,WAAW;AACnBA,wBAAAA,MAAa,MAAA,QAAA,gDAAA,uBAAuB,SAAS;AAAA,QAC7C;AAGD,oBAAY,QAAQ;AACpBA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,0CAA0C;AACtDA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAED,cAAM,kBAAkB,MAAMC,8CAAuB,cAAc,KAAK;AACxED,sBAAAA,MAAY,MAAA,OAAA,gDAAA,2BAA2B;AACvCA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,eAAe;AAC3BA,sBAAY,MAAA,MAAA,OAAA,gDAAA,qBAAqB,OAAO,eAAe;AACvDA,4BAAY,MAAA,OAAA,gDAAA,qBAAqB,kBAAkB,gBAAgB,SAAS,CAAC;AAC7EA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,sCAAsC;AAGlD,YAAI,CAAC,mBAAmB,gBAAgB,KAAI,EAAG,WAAW,GAAG;AAC5D,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QACtC;AAGD,8BAAsB,QAAQ;AAG9B,oBAAY,QAAQ;AACpBA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,yCAAyC;AACrDA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAGD,cAAM,iBAAiB;AAAA;AAAA;AAAA,EAGxB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CdA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,8BAA8B;AAC1CA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,+BAA+B;AAC3CA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,eAAe;AAC3BA,4BAAY,MAAA,OAAA,gDAAA,8BAA8B,eAAe,SAAS,eAAe,CAAC;AAClFA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,4BAA4B;AACxCA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,cAAc;AAC1BA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,qCAAqC;AAEjD,cAAM,mBAAmB,MAAME,iBAAe,gBAAC,cAAc;AAC7DF,sBAAAA,MAAA,MAAA,OAAA,gDAAY,oCAAoC;AAChDA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,4BAA4B;AACxCA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,gBAAgB;AAC5BA,sBAAY,MAAA,MAAA,OAAA,gDAAA,mBAAmB,OAAO,gBAAgB;AACtDA,4BAAY,MAAA,OAAA,gDAAA,mBAAmB,mBAAmB,iBAAiB,SAAS,CAAC;AAC7EA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,oCAAoC;AAGhDA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,oCAAoC;AAChD,YAAI;AACJ,YAAI;AAEH,gBAAM,kBAAkB,iBAAiB,QAAQ,sBAAsB,EAAE,EAAE,KAAM;AACjFA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,qBAAqB;AACjCA,wBAAAA,MAAA,MAAA,OAAA,gDAAY,eAAe;AAC3BA,wBAAAA,MAAA,MAAA,OAAA,gDAAY,uBAAuB;AAEnC,yBAAe,KAAK,MAAM,eAAe;AACzCA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,gCAAgC;AAC5CA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,mBAAmB;AAC/BA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,YAAY;AAGxBA,wBAAAA,MAAA,MAAA,OAAA,gDAAY,iBAAiB;AAC7BA,wBAAA,MAAA,MAAA,OAAA,gDAAY,yBAAyB,aAAa,QAAQ;AAC1DA,wBAAY,MAAA,MAAA,OAAA,gDAAA,2BAA2B,aAAa,UAAU;AAC9DA,wBAAA,MAAA,MAAA,OAAA,gDAAY,wBAAwB,aAAa,OAAO;AACxDA,wBAAY,MAAA,MAAA,OAAA,gDAAA,0BAA0B,aAAa,SAAS;AAAA,QAE5D,SAAQ,YAAY;AACpBA,wBAAAA,MAAc,MAAA,SAAA,gDAAA,gCAAgC;AAC9CA,wBAAAA,MAAA,MAAA,SAAA,gDAAc,mBAAmB,UAAU;AAC3CA,6FAAc,mBAAmB,WAAW,OAAO;AACnDA,wBAAAA,qEAAc,qBAAqB,gBAAgB;AACnDA,wBAAAA,MAAc,MAAA,SAAA,gDAAA,oBAAoB,iBAAiB,QAAQ,sBAAsB,EAAE,EAAE,KAAI,CAAE;AAE3FA,wBAAAA,MAAA,MAAA,OAAA,gDAAY,uBAAuB;AAGnC,cAAI,oBAAoB;AACxB,cAAI,mBAAmB,gBAAgB,SAAS,GAAG;AAElD,kBAAM,eAAe,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAChF,uBAAW,WAAW,cAAc;AACnC,kBAAI,gBAAgB,SAAS,OAAO,GAAG;AACtC,oCAAoB;AACpB;AAAA,cACA;AAAA,YACD;AAAA,UACD;AAED,yBAAe;AAAA,YACd,UAAU;AAAA,YACV,YAAY;AAAA;AAAA,YACZ,SAAS;AAAA,YACT,WAAW;AAAA,cACV,UAAU;AAAA,cACV,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACP;AAAA,YACD,cAAc;AAAA,cACb,UAAU,iBAAiB;AAAA,cAC3B;AAAA,cACA;AAAA,YACA;AAAA,YACD,kBAAkB;AAAA,cACjB,QAAQ,CAAC,UAAU,iBAAiB,UAAU;AAAA,cAC9C,UAAU,CAAC,eAAe;AAAA,cAC1B,aAAa,CAAC,kCAAkC;AAAA,YAChD;AAAA,UACD;AACDA,wBAAAA,mEAAY,yBAAyB,YAAY;AACjDA,wBAAAA,MAAA,MAAA,OAAA,gDAAY,sBAAsB,iBAAiB;AAAA,QACnD;AAGDA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,gCAAgC;AAC5CA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,mBAAmB;AAC/BA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,YAAY;AAGxB,cAAM,iBAAiB,CAAC,YAAY,cAAc,WAAW,aAAa,gBAAgB,kBAAkB;AAC5G,cAAM,gBAAgB,eAAe,OAAO,WAAS,CAAC,aAAa,KAAK,CAAC;AACzE,YAAI,cAAc,SAAS,GAAG;AAC7BA,wBAAAA,MAAA,MAAA,QAAA,gDAAa,0BAA0B,aAAa;AAAA,QACpD;AAED,uBAAe,QAAQ;AACvBA,sBAAAA,MAAY,MAAA,OAAA,gDAAA,yBAAyB;AACrCA,sBAAA,MAAA,MAAA,OAAA,gDAAY,mCAAmC,eAAe,KAAK;AAGnE,cAAM,YAAY;AAAA,UACjB,IAAI,KAAK,IAAK;AAAA,UACd,OAAO,cAAc;AAAA,UACrB,UAAU,eAAe,MAAM;AAAA,UAC/B,SAAS,eAAe,MAAM;AAAA,UAC9B,WAAW,KAAK,IAAK;AAAA,UACrB,YAAY,eAAe,MAAM;AAAA,UACjC,WAAW,eAAe,MAAM;AAAA,UAChC,cAAc,eAAe,MAAM;AAAA,UACnC,kBAAkB,eAAe,MAAM;AAAA,UACvC,OAAM,oBAAI,QAAO,mBAAmB,SAAS;AAAA,YAC5C,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,UACb,CAAK;AAAA,QACD;AACD,0BAAkB,SAAS;AAE3BA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAAA,MAED,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,mBAAmB,KAAK;AACtCA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,mBAAmB;AAAA,UAChC,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,UACb,MAAM,MAAM;AAAA,QAChB,CAAI;AACDA,sBAAAA,MAAI,YAAa;AAGjB,YAAI,aAAa;AACjB,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AAClD,uBAAa;AACb,uBAAa,QAAQ;AACrBA,wBAAc,MAAA,MAAA,SAAA,gDAAA,mBAAmB,MAAM,OAAO;AAAA,QAClD,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AAC3D,uBAAa;AACb,uBAAa,QAAQ;AACrBA,wBAAA,MAAA,MAAA,SAAA,gDAAc,2BAA2B,MAAM,OAAO;AAAA,QAC1D,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,MAAM,GAAG;AAC3D,uBAAa;AACb,uBAAa,QAAQ;AACrBA,wBAAA,MAAA,MAAA,SAAA,gDAAc,6BAA6B,MAAM,OAAO;AAAA,QAC5D,OAAU;AACN,uBAAa,QAAQ;AACrBA,wBAAc,MAAA,MAAA,SAAA,gDAAA,mBAAmB,MAAM,OAAO;AAAA,QAC9C;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACd,CAAI;AAAA,MACJ,UAAY;AACT,kBAAU,QAAQ;AAClB,oBAAY,QAAQ;AAAA,MACpB;AAAA,IACD;AAGD,UAAM,aAAa,CAAC,YAAY;AAC/B,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAGD,UAAM,mBAAmB,CAAC,YAAY;AACrC,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAmED,UAAM,qBAAqB,MAAM;AAChC,UAAI,CAAC,eAAe;AAAO;AAE3B,YAAM,SAAS;AAAA,QACd,IAAI,KAAK,IAAK;AAAA,QACd,OAAO,cAAc;AAAA,QACrB,UAAU,eAAe,MAAM;AAAA,QAC/B,YAAY,eAAe,MAAM;AAAA,QACjC,SAAS,eAAe,MAAM;AAAA,QAC9B,WAAW,eAAe,MAAM;AAAA,QAChC,cAAc,eAAe,MAAM;AAAA,QACnC,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,MACnC;AAED,YAAM,kBAAkBA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AACvE,sBAAgB,QAAQ,MAAM;AAC9BA,0BAAI,eAAe,uBAAuB,eAAe;AAEzD,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACT,CAAG;AAAA,IACD;AAED,UAAM,qBAAqB,MAAM;AAChC,YAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,qBAAe,QAAQ;AAAA,IACvB;AAED,UAAM,oBAAoB,CAAC,WAAW;AACrC,UAAI;AACH,cAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,gBAAQ,QAAQ,MAAM;AAEtB,YAAI,QAAQ,SAAS,IAAI;AACxB,kBAAQ,OAAO,EAAE;AAAA,QACjB;AACDA,4BAAI,eAAe,uBAAuB,OAAO;AACjD,uBAAe,QAAQ;AAAA,MACvB,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,aAAa,KAAK;AAAA,MAChC;AAAA,IACD;AAED,UAAM,aAAa,CAAC,cAAc;AACjC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK,QAAS;AAC1B,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,aAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,OAAO;AAAA,IAC3C;AAED,UAAM,oBAAoB,CAAC,WAAW;AAErC,qBAAe,QAAQ;AACvB,oBAAc,QAAQ,OAAO;AAAA,IAC7B;AAED,UAAM,iBAAiB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACR,CAAG;AAAA,IACD;AAEDG,kBAAAA,UAAU,MAAM;AACf,yBAAoB;AAAA,IACtB,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1rBF,GAAG,WAAW,eAAe;"}
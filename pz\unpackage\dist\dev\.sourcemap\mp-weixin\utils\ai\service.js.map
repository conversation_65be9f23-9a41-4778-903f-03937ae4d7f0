{"version": 3, "file": "service.js", "sources": ["utils/ai/service.js"], "sourcesContent": ["import { DEEPSEEK_BASE_URL, DEEPSEEK_API_KEY, AI_CONFIG } from './config.js';\n\nclass AIService {\n  constructor(baseUrl, apiKey) {\n    this.baseUrl = baseUrl;\n    this.apiKey = apiKey;\n  }\n  // 获取当前位置信息\n  async getCurrentLocation() {\n    return new Promise((resolve, reject) => {\n      uni.getLocation({\n        type: MAP_CONFIG.LOCATION_TYPES.GCJ02,\n        isHighAccuracy: true, // 开启高精度定位\n        highAccuracyExpireTime: 3000,\n        timeout: LOCATION_CONFIG.TIMEOUT,\n        success: async (res) => {\n          try {\n            console.log('获取到的原始位置信息:', {\n              latitude: res.latitude,\n              longitude: res.longitude\n            });\n\n            const addressRes = await uni.request({\n              url: MAP_CONFIG.GEOCODER_URL,\n              method: 'GET',\n              data: {\n                location: `${res.latitude},${res.longitude}`,\n                key: MAP_CONFIG.API_KEY,\n                ...MAP_CONFIG.PARAMS\n              }\n            });\n\n            console.log('腾讯地图API响应:', addressRes);\n\n            if (addressRes.statusCode === 200 && addressRes.data.status === 0) {\n              const addressInfo = addressRes.data.result;\n              const addressComponent = addressInfo.address_component;\n\n              const locationDetail = {\n                latitude: res.latitude,\n                longitude: res.longitude,\n                address: addressInfo.formatted_addresses?.recommend || addressInfo.address,\n                district: addressComponent.district,\n                city: addressComponent.city,\n                province: addressComponent.province,\n                street: addressComponent.street,\n                formatted_address: addressInfo.formatted_addresses?.recommend\n              };\n\n              console.log('解析后的位置详情:', locationDetail);\n              resolve(locationDetail);\n            } else {\n              console.error('地图API错误:', addressRes.data);\n              reject(new Error(LOCATION_CONFIG.ERROR_MESSAGES.GEOCODER_FAIL));\n            }\n          } catch (error) {\n            console.error('地址解析错误:', error);\n            reject(error);\n          }\n        },\n        fail: (error) => {\n          console.error('获取位置失败:', error);\n          reject(new Error(LOCATION_CONFIG.ERROR_MESSAGES.LOCATION_FAIL));\n        }\n      });\n    });\n  }\n\n  // 发送消息到AI（带重试机制）\n  async sendMessageWithRetry(message, maxRetries = AI_CONFIG.MAX_RETRIES) {\n    let retries = 0;\n\n    while (retries < maxRetries) {\n      try {\n        const response = await this.sendMessage(message);\n        return response;\n      } catch (error) {\n        retries++;\n        console.error(`AI服务调用失败 (${retries}/${maxRetries}):`, error);\n\n        if (retries === maxRetries) {\n          throw new Error(AI_CONFIG.ERROR_MESSAGES.REQUEST_FAIL);\n        }\n\n        // 指数退避重试\n        const delay = Math.min(AI_CONFIG.RETRY_DELAY * Math.pow(2, retries) + Math.random() * 1000, 15000);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n  }\n\n  // 发送消息到AI\n  async sendMessage(message) {\n    try {\n      const requestData = {\n        model: 'deepseek-chat',\n        messages: [\n          {\n            role: 'system',\n            content: '你是一个专业的医疗助手，请用中文简洁地回答问题。'\n          },\n          {\n            role: 'user',\n            content: message\n          }\n        ],\n        temperature: 0.7,\n        max_tokens: 2000,\n        stream: false\n      };\n\n      const response = await uni.request({\n        url: this.baseUrl,\n        method: 'POST',\n        data: requestData,\n        header: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Accept': 'application/json'\n        },\n        timeout: AI_CONFIG.TIMEOUT,\n        enableHttp2: true,\n        enableQuic: true,\n        enableCache: true\n      });\n\n      if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices[0]) {\n        return response.data.choices[0].message.content;\n      } else {\n        throw new Error(`AI服务响应异常: ${response.statusCode}`);\n      }\n    } catch (error) {\n      console.error('AI服务请求失败：', error);\n      if (error.errMsg && error.errMsg.includes('timeout')) {\n        throw new Error(AI_CONFIG.ERROR_MESSAGES.TIMEOUT);\n      } else if (error.errMsg && error.errMsg.includes('fail')) {\n        throw new Error(AI_CONFIG.ERROR_MESSAGES.NETWORK_ERROR);\n      }\n      throw error;\n    }\n  }\n}\n\n// 创建DeepSeek服务实例\nconst deepseekService = new AIService(DEEPSEEK_BASE_URL, DEEPSEEK_API_KEY);\n\n// 导出发送消息方法\nexport async function sendMessageToAI(message) {\n  try {\n    return await deepseekService.sendMessageWithRetry(message);\n  } catch (error) {\n    console.error('调用AI服务失败:', error);\n    throw error;\n  }\n}\n\n// 导出获取位置并发送到AI的方法\nexport async function sendLocationBasedMessage(message) {\n  try {\n    const location = await deepseekService.getCurrentLocation();\n    const locationMessage = `基于以下位置信息：\n当前位置：${location.formatted_address || location.address}\n省份：${location.province}\n城市：${location.city}\n区域：${location.district}\n街道：${location.street}\n经度：${location.longitude}\n纬度：${location.latitude}\n\n${message}`;\n\n    return await deepseekService.sendMessageWithRetry(locationMessage);\n  } catch (error) {\n    console.error('获取位置信息失败:', error);\n    throw error;\n  }\n}"], "names": ["uni", "AI_CONFIG", "DEEPSEEK_BASE_URL", "DEEPSEEK_API_KEY"], "mappings": ";;;AAEA,MAAM,UAAU;AAAA,EACd,YAAY,SAAS,QAAQ;AAC3B,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EACf;AAAA;AAAA,EAED,MAAM,qBAAqB;AACzB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM,WAAW,eAAe;AAAA,QAChC,gBAAgB;AAAA;AAAA,QAChB,wBAAwB;AAAA,QACxB,SAAS,gBAAgB;AAAA,QACzB,SAAS,OAAO,QAAQ;;AACtB,cAAI;AACFA,0BAAAA,MAAY,MAAA,OAAA,6BAAA,eAAe;AAAA,cACzB,UAAU,IAAI;AAAA,cACd,WAAW,IAAI;AAAA,YAC7B,CAAa;AAED,kBAAM,aAAa,MAAMA,cAAG,MAAC,QAAQ;AAAA,cACnC,KAAK,WAAW;AAAA,cAChB,QAAQ;AAAA,cACR,MAAM;AAAA,gBACJ,UAAU,GAAG,IAAI,QAAQ,IAAI,IAAI,SAAS;AAAA,gBAC1C,KAAK,WAAW;AAAA,gBAChB,GAAG,WAAW;AAAA,cACf;AAAA,YACf,CAAa;AAEDA,0BAAY,MAAA,MAAA,OAAA,6BAAA,cAAc,UAAU;AAEpC,gBAAI,WAAW,eAAe,OAAO,WAAW,KAAK,WAAW,GAAG;AACjE,oBAAM,cAAc,WAAW,KAAK;AACpC,oBAAM,mBAAmB,YAAY;AAErC,oBAAM,iBAAiB;AAAA,gBACrB,UAAU,IAAI;AAAA,gBACd,WAAW,IAAI;AAAA,gBACf,WAAS,iBAAY,wBAAZ,mBAAiC,cAAa,YAAY;AAAA,gBACnE,UAAU,iBAAiB;AAAA,gBAC3B,MAAM,iBAAiB;AAAA,gBACvB,UAAU,iBAAiB;AAAA,gBAC3B,QAAQ,iBAAiB;AAAA,gBACzB,oBAAmB,iBAAY,wBAAZ,mBAAiC;AAAA,cACpE;AAEcA,4BAAY,MAAA,MAAA,OAAA,6BAAA,aAAa,cAAc;AACvC,sBAAQ,cAAc;AAAA,YACpC,OAAmB;AACLA,4BAAc,MAAA,MAAA,SAAA,6BAAA,YAAY,WAAW,IAAI;AACzC,qBAAO,IAAI,MAAM,gBAAgB,eAAe,aAAa,CAAC;AAAA,YAC/D;AAAA,UACF,SAAQ,OAAO;AACdA,0BAAA,MAAA,MAAA,SAAA,6BAAc,WAAW,KAAK;AAC9B,mBAAO,KAAK;AAAA,UACb;AAAA,QACF;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAc,MAAA,MAAA,SAAA,6BAAA,WAAW,KAAK;AAC9B,iBAAO,IAAI,MAAM,gBAAgB,eAAe,aAAa,CAAC;AAAA,QAC/D;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,qBAAqB,SAAS,aAAaC,gBAAAA,UAAU,aAAa;AACtE,QAAI,UAAU;AAEd,WAAO,UAAU,YAAY;AAC3B,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAY,OAAO;AAC/C,eAAO;AAAA,MACR,SAAQ,OAAO;AACd;AACAD,sBAAAA,MAAA,MAAA,SAAA,6BAAc,aAAa,OAAO,IAAI,UAAU,MAAM,KAAK;AAE3D,YAAI,YAAY,YAAY;AAC1B,gBAAM,IAAI,MAAMC,gBAAAA,UAAU,eAAe,YAAY;AAAA,QACtD;AAGD,cAAM,QAAQ,KAAK,IAAIA,gBAAAA,UAAU,cAAc,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK,OAAM,IAAK,KAAM,IAAK;AACjG,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,CAAC;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,YAAY,SAAS;AACzB,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,QACF;AAAA,QACD,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,MAChB;AAEM,YAAM,WAAW,MAAMD,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK,KAAK;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,iBAAiB,UAAU,KAAK,MAAM;AAAA,UACtC,UAAU;AAAA,QACX;AAAA,QACD,SAASC,gBAAS,UAAC;AAAA,QACnB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,MACrB,CAAO;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,QAAQ,SAAS,KAAK,WAAW,SAAS,KAAK,QAAQ,CAAC,GAAG;AACrG,eAAO,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAAA,MAChD,OAAa;AACL,cAAM,IAAI,MAAM,aAAa,SAAS,UAAU,EAAE;AAAA,MACnD;AAAA,IACF,SAAQ,OAAO;AACdD,uEAAc,aAAa,KAAK;AAChC,UAAI,MAAM,UAAU,MAAM,OAAO,SAAS,SAAS,GAAG;AACpD,cAAM,IAAI,MAAMC,gBAAAA,UAAU,eAAe,OAAO;AAAA,MACxD,WAAiB,MAAM,UAAU,MAAM,OAAO,SAAS,MAAM,GAAG;AACxD,cAAM,IAAI,MAAMA,gBAAAA,UAAU,eAAe,aAAa;AAAA,MACvD;AACD,YAAM;AAAA,IACP;AAAA,EACF;AACH;AAGA,MAAM,kBAAkB,IAAI,UAAUC,gBAAiB,mBAAEC,gBAAgB,gBAAA;AAGlE,eAAe,gBAAgB,SAAS;AAC7C,MAAI;AACF,WAAO,MAAM,gBAAgB,qBAAqB,OAAO;AAAA,EAC1D,SAAQ,OAAO;AACdH,kBAAA,MAAA,MAAA,SAAA,8BAAc,aAAa,KAAK;AAChC,UAAM;AAAA,EACP;AACH;;"}
// 新食物识别功能测试文件
// 测试通义千问VL-MAX + DeepSeek集成方案

import { analyzeImageWithQwenVL } from './utils/ai/qwenVLService.js';
import { sendMessageToAI } from './utils/ai/service.js';

/**
 * 测试新的食物识别流程
 */
async function testNewFoodRecognition() {
  console.log('🔍 [测试] ===== 开始测试新的食物识别功能 =====');
  
  try {
    // 模拟图片路径（实际使用时会是真实的图片路径）
    const testImagePath = '/path/to/test/food/image.jpg';
    
    console.log('🔍 [测试] 步骤1: 测试通义千问VL-MAX图像识别...');
    
    // 第一步：通义千问VL-MAX图像识别
    const qwenDescription = await analyzeImageWithQwenVL(testImagePath);
    console.log('🔍 [测试] 通义千问VL-MAX识别结果:');
    console.log(qwenDescription);
    console.log('🔍 [测试] ===== 通义千问VL-MAX识别完成 =====');
    
    console.log('🔍 [测试] 步骤2: 测试DeepSeek结构化处理...');
    
    // 第二步：DeepSeek结构化处理
    const deepSeekPrompt = `作为专业的营养师，请基于以下详细的食物图像描述，分析食物的营养成分和健康建议：

${qwenDescription}

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "食物名称",
  "confidence": 85,
  "giValue": 65,
  "nutrition": {
    "calories": 180,
    "carbs": 35,
    "protein": 8,
    "fat": 6,
    "fiber": 4,
    "sugar": 2
  },
  "healthAdvice": [
    "健康建议1",
    "健康建议2",
    "健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "推荐食用的食物及原因"
    ],
    "limitEat": [
      "需要限制的食物及原因"
    ],
    "suggestions": [
      "具体的饮食建议"
    ]
  }
}

要求：
1. 所有数值必须是合理的营养数据
2. GI值范围在0-100之间
3. 健康建议要针对糖尿病患者
4. 分析要专业且实用
5. 返回纯JSON格式，不要markdown代码块`;

    console.log('🔍 [测试] 发送给DeepSeek的请求内容:');
    console.log(deepSeekPrompt);
    
    const deepSeekResponse = await sendMessageToAI(deepSeekPrompt);
    console.log('🔍 [测试] DeepSeek返回的结果:');
    console.log(deepSeekResponse);
    console.log('🔍 [测试] ===== DeepSeek处理完成 =====');
    
    // 第三步：解析JSON结果
    console.log('🔍 [测试] 步骤3: 解析JSON结果...');
    
    let parsedResult;
    try {
      const cleanedResponse = deepSeekResponse.replace(/```json\s*|\s*```/g, '').trim();
      parsedResult = JSON.parse(cleanedResponse);
      console.log('🔍 [测试] JSON解析成功:', parsedResult);
    } catch (parseError) {
      console.error('🔍 [测试] JSON解析失败:', parseError);
      console.error('🔍 [测试] 原始响应内容:', deepSeekResponse);
      throw parseError;
    }
    
    console.log('🔍 [测试] ===== 新食物识别功能测试完成 =====');
    console.log('🔍 [测试] 最终结果:', parsedResult);
    
    return {
      success: true,
      qwenDescription,
      deepSeekResponse,
      parsedResult
    };
    
  } catch (error) {
    console.error('🔍 [测试] 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试API连接
 */
async function testAPIConnections() {
  console.log('🔍 [测试] ===== 开始测试API连接 =====');
  
  try {
    // 测试DeepSeek连接
    console.log('🔍 [测试] 测试DeepSeek API连接...');
    const testMessage = await sendMessageToAI('测试连接');
    console.log('🔍 [测试] DeepSeek连接成功:', testMessage);
    
    // 测试通义千问VL-MAX连接
    console.log('🔍 [测试] 测试通义千问VL-MAX API连接...');
    // 这里需要实际的图片才能测试，暂时跳过
    console.log('🔍 [测试] 通义千问VL-MAX连接测试需要实际图片，跳过');
    
    console.log('🔍 [测试] ===== API连接测试完成 =====');
    return true;
    
  } catch (error) {
    console.error('🔍 [测试] API连接测试失败:', error);
    return false;
  }
}

/**
 * 检查配置
 */
function checkConfiguration() {
  console.log('🔍 [测试] ===== 检查配置信息 =====');
  
  try {
    const { QWEN_VL_CONFIG } = require('./utils/ai/config.js');
    const { DEEPSEEK_API_KEY } = require('./utils/ai/config.js');
    
    console.log('🔍 [测试] 通义千问VL-MAX配置:');
    console.log('  - API Key:', QWEN_VL_CONFIG.API_KEY ? '已配置' : '未配置');
    console.log('  - Base URL:', QWEN_VL_CONFIG.BASE_URL);
    console.log('  - Model:', QWEN_VL_CONFIG.MODEL);
    
    console.log('🔍 [测试] DeepSeek配置:');
    console.log('  - API Key:', DEEPSEEK_API_KEY ? '已配置' : '未配置');
    
    console.log('🔍 [测试] ===== 配置检查完成 =====');
    return true;
    
  } catch (error) {
    console.error('🔍 [测试] 配置检查失败:', error);
    return false;
  }
}

// 导出测试函数
export {
  testNewFoodRecognition,
  testAPIConnections,
  checkConfiguration
};

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  // Node.js环境
  (async () => {
    console.log('🔍 [测试] 开始执行所有测试...');
    
    checkConfiguration();
    await testAPIConnections();
    // await testNewFoodRecognition(); // 需要实际图片才能测试
    
    console.log('🔍 [测试] 所有测试执行完成');
  })();
}

# 小程序环境测试指南

## 测试目标

验证新的食物识别功能（通义千问VL-MAX + DeepSeek）在小程序环境中的完整工作流程。

## 测试准备

### 1. 确认文件更新
确保以下文件已更新：
- ✅ `pz/utils/ai/config.js` - 添加通义千问VL-MAX配置
- ✅ `pz/utils/ai/qwenVLService.js` - 通义千问VL-MAX服务
- ✅ `pz/pages/blood-glucose/food-analysis.vue` - 重构的analyzeFood函数
- ✅ `pz/utils/ai/service.js` - DeepSeek服务（已有）

### 2. 配置验证
```javascript
// 通义千问VL-MAX配置
QWEN_VL_CONFIG = {
  BASE_URL: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
  API_KEY: 'sk-948691927af4402a92cb566e1d8e153f',
  MODEL: 'qwen-vl-max'
}

// DeepSeek配置
DEEPSEEK_API_KEY = '***********************************'
DEEPSEEK_BASE_URL = 'https://api.deepseek.com/v1/chat/completions'
```

## 测试步骤

### 第一步：基础功能测试

1. **打开小程序开发工具**
2. **导航到血糖模块 > 饮食分析页面**
3. **打开开发者工具控制台**
4. **选择一张清晰的食物图片**
5. **点击"开始分析"按钮**

### 第二步：关键日志监控

在控制台中查找以下关键输出：

#### 1. 通义千问VL-MAX识别阶段
```
🔍 [食物识别] ===== 第一步：通义千问VL-MAX图像识别 =====
🔍 [通义千问VL-MAX] 开始分析食物图片...
🔍 [通义千问VL-MAX] 图片路径: [图片路径]
🔍 [通义千问VL-MAX] 发送请求到API...
🔍 [通义千问VL-MAX] API响应状态: 200
🔍 [通义千问VL-MAX] API响应数据: [响应数据]
🔍 [通义千问VL-MAX] 图像识别成功
🔍 [通义千问VL-MAX] 识别结果: [详细的食物描述]
🔍 [食物识别] ===== 通义千问VL-MAX识别完成 =====
```

#### 2. DeepSeek结构化处理阶段
```
🔍 [食物识别] ===== 第二步：DeepSeek结构化处理 =====
🔍 [食物识别] 发送给DeepSeek的请求内容:
[完整的营养分析prompt]
🔍 [食物识别] DeepSeek返回的结果:
[JSON格式的营养分析结果]
🔍 [食物识别] ===== DeepSeek处理完成 =====
```

#### 3. JSON解析阶段
```
🔍 [食物识别] JSON解析成功: [解析后的对象]
```

### 第三步：网络请求监控

在小程序开发工具的**网络面板**中检查：

#### 1. 通义千问VL-MAX请求
- **URL**: `https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation`
- **方法**: POST
- **状态**: 200 OK
- **响应**: 包含图像描述的JSON数据

#### 2. DeepSeek请求
- **URL**: `https://api.deepseek.com/v1/chat/completions`
- **方法**: POST
- **状态**: 200 OK
- **响应**: 包含营养分析的JSON数据

### 第四步：结果验证

#### 1. 前端显示检查
- ✅ 食物名称正确显示
- ✅ GI值在合理范围内（0-100）
- ✅ 营养成分数据完整
- ✅ 健康建议针对糖尿病患者
- ✅ 详细分析内容丰富

#### 2. 数据结构验证
```javascript
// 期望的数据结构
{
  "foodName": "具体食物名称",
  "confidence": 数字(0-100),
  "giValue": 数字(0-100),
  "nutrition": {
    "calories": 数字,
    "carbs": 数字,
    "protein": 数字,
    "fat": 数字,
    "fiber": 数字,
    "sugar": 数字
  },
  "healthAdvice": ["建议1", "建议2", "建议3"],
  "detailedAnalysis": {
    "canEat": ["推荐内容"],
    "limitEat": ["限制内容"],
    "suggestions": ["建议内容"]
  }
}
```

## 常见问题排查

### 1. 通义千问VL-MAX失败
**症状**：第一步识别失败
**检查**：
- API Key是否正确
- 网络连接是否正常
- 图片格式是否支持
- 图片大小是否合适

**解决**：
```javascript
// 检查错误信息
🔍 [通义千问VL-MAX] 图像识别失败: [错误信息]
```

### 2. DeepSeek API失败
**症状**：第二步处理失败
**检查**：
- DeepSeek API Key是否有效
- 网络请求是否被拦截
- 请求格式是否正确

**解决**：
```javascript
// 检查错误信息
🔍 [食物识别] DeepSeek API错误: [错误信息]
```

### 3. JSON解析失败
**症状**：第三步解析失败
**检查**：
- DeepSeek返回的数据格式
- 是否包含markdown代码块
- JSON结构是否完整

**解决**：
```javascript
// 查看原始响应
🔍 [食物识别] 原始响应内容: [原始数据]
// 使用备用数据结构
🔍 [食物识别] 使用备用数据结构: [备用数据]
```

## 性能测试

### 1. 响应时间测试
- **通义千问VL-MAX**: 预期 10-30秒
- **DeepSeek处理**: 预期 5-15秒
- **总体流程**: 预期 15-45秒

### 2. 成功率测试
- 测试不同类型的食物图片
- 测试不同质量的图片
- 测试边界情况（模糊、暗光等）

## 测试报告模板

```
测试时间: [时间]
测试环境: 小程序开发工具
测试图片: [图片描述]

=== 测试结果 ===
✅/❌ 通义千问VL-MAX识别: [成功/失败 + 详情]
✅/❌ DeepSeek结构化处理: [成功/失败 + 详情]  
✅/❌ JSON解析: [成功/失败 + 详情]
✅/❌ 前端显示: [成功/失败 + 详情]

=== 性能数据 ===
- 图像识别耗时: [秒]
- 结构化处理耗时: [秒]
- 总耗时: [秒]

=== 问题记录 ===
[如有问题，详细记录错误信息和解决方案]

=== 改进建议 ===
[基于测试结果的改进建议]
```

## 下一步行动

1. **立即测试**：在小程序开发工具中运行完整流程
2. **记录结果**：详细记录控制台输出和网络请求
3. **问题反馈**：如有问题，提供具体的错误信息
4. **优化调整**：根据测试结果进行必要的代码调整

// DeepSeek API配置
export const DEEPSEEK_BASE_URL = 'https://api.deepseek.com/v1/chat/completions';
export const DEEPSEEK_API_KEY = '***********************************';

// 腾讯地图API配置
export const MAP_CONFIG = {
  API_KEY: '4Q3BZ-45IKJ-HDVFH-DHRJ2-KHCBT-VTFHH',
  GEOCODER_URL: 'https://apis.map.qq.com/ws/geocoder/v1/',
  LOCATION_TYPES: {
    GCJ02: 'gcj02'
  },
  TIMEOUT: 30000,
  PARAMS: {
    get_poi: 1,
    output: 'json',
    coord_type: 5
  }
};

// 位置服务配置
export const LOCATION_CONFIG = {
  TIMEOUT: 30000,
  ERROR_MESSAGES: {
    LOCATION_FAIL: '获取位置信息失败，请检查是否已授权位置权限',
    GEOCODER_FAIL: '获取地址信息失败',
    TIMEOUT: '获取位置信息超时'
  }
};

// 通义千问VL-MAX配置
export const QWEN_VL_CONFIG = {
  BASE_URL: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
  API_KEY: 'sk-948691927af4402a92cb566e1d8e153f',
  MODEL: 'qwen-vl-max',
  TIMEOUT: 60000,
  MAX_RETRIES: 3
};

// AI服务配置
export const AI_CONFIG = {
  TIMEOUT: 120000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000,
  ERROR_MESSAGES: {
    REQUEST_FAIL: 'AI服务请求失败，请稍后重试',
    TIMEOUT: 'AI服务响应超时，请稍后重试',
    NETWORK_ERROR: '网络连接失败，请检查网络设置'
  }
};


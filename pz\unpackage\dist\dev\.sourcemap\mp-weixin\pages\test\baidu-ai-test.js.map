{"version": 3, "file": "baidu-ai-test.js", "sources": ["pages/test/baidu-ai-test.vue", "D:/bc Files/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9iYWlkdS1haS10ZXN0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"test-container\">\n    <view class=\"header\">\n      <text class=\"title\">百度AI食物识别测试</text>\n      <text class=\"subtitle\">用于验证优化后的识别效果</text>\n    </view>\n\n    <!-- API连接测试 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">🔧 API连接测试</view>\n      <view class=\"test-buttons\">\n        <button @click=\"testConnection\" :disabled=\"testing\" class=\"test-btn\">\n          {{ testing ? '基础测试中...' : '基础连接测试' }}\n        </button>\n        <button @click=\"realAPITest\" :disabled=\"realTesting\" class=\"test-btn secondary\">\n          {{ realTesting ? '实际测试中...' : '实际API测试' }}\n        </button>\n      </view>\n      <view v-if=\"connectionResult\" class=\"result-box\">\n        <text class=\"result-title\">基础连接测试结果：</text>\n        <text :class=\"connectionResult.success ? 'success' : 'error'\">\n          {{ connectionResult.success ? '✅ 连接成功' : '❌ 连接失败' }}\n        </text>\n        <view v-if=\"connectionResult.issues.length > 0\" class=\"issues\">\n          <text class=\"issues-title\">发现问题：</text>\n          <text v-for=\"issue in connectionResult.issues\" :key=\"issue\" class=\"issue-item\">{{ issue }}</text>\n        </view>\n      </view>\n\n      <view v-if=\"realTestResult\" class=\"result-box\">\n        <text class=\"result-title\">实际API测试结果：</text>\n        <view class=\"api-test-summary\">\n          <text class=\"test-stats\">成功: {{ realTestResult.summary.successCount }}/{{ realTestResult.summary.totalTests }}</text>\n          <text class=\"test-stats\">失败: {{ realTestResult.summary.failureCount }}/{{ realTestResult.summary.totalTests }}</text>\n        </view>\n\n        <view v-if=\"realTestResult.dishAPI\" class=\"api-result\">\n          <text class=\"api-name\">🍽️ 菜品识别API:</text>\n          <text :class=\"realTestResult.dishAPI.success ? 'success' : 'error'\">\n            {{ realTestResult.dishAPI.success ? '✅ 成功' : '❌ 失败' }}\n          </text>\n          <text v-if=\"!realTestResult.dishAPI.success\" class=\"error-detail\">\n            {{ realTestResult.dishAPI.error }}\n          </text>\n        </view>\n\n        <view v-if=\"realTestResult.ingredientAPI\" class=\"api-result\">\n          <text class=\"api-name\">🥬 果蔬识别API:</text>\n          <text :class=\"realTestResult.ingredientAPI.success ? 'success' : 'error'\">\n            {{ realTestResult.ingredientAPI.success ? '✅ 成功' : '❌ 失败' }}\n          </text>\n          <text v-if=\"!realTestResult.ingredientAPI.success\" class=\"error-detail\">\n            {{ realTestResult.ingredientAPI.error }}\n          </text>\n        </view>\n\n        <view v-if=\"realTestResult.imageUnderstandingAPI\" class=\"api-result\">\n          <text class=\"api-name\">🔍 图像理解API:</text>\n          <text :class=\"realTestResult.imageUnderstandingAPI.success ? 'success' : 'error'\">\n            {{ realTestResult.imageUnderstandingAPI.success ? '✅ 成功' : '❌ 失败' }}\n          </text>\n          <text v-if=\"!realTestResult.imageUnderstandingAPI.success\" class=\"error-detail\">\n            {{ realTestResult.imageUnderstandingAPI.error }}\n          </text>\n        </view>\n\n        <view v-if=\"realTestResult.summary.issues.length > 0\" class=\"issues\">\n          <text class=\"issues-title\">发现问题：</text>\n          <text v-for=\"issue in realTestResult.summary.issues\" :key=\"issue\" class=\"issue-item\">{{ issue }}</text>\n        </view>\n\n        <view v-if=\"realTestResult.summary.recommendations.length > 0\" class=\"recommendations\">\n          <text class=\"rec-title\">建议：</text>\n          <text v-for=\"rec in realTestResult.summary.recommendations\" :key=\"rec\" class=\"rec-item\">{{ rec }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 图片识别测试 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">🍽️ 图片识别测试</view>\n      <button @click=\"selectImage\" :disabled=\"recognizing\" class=\"test-btn\">\n        {{ recognizing ? '识别中...' : '选择图片测试' }}\n      </button>\n\n      <view v-if=\"selectedImage\" class=\"image-preview\">\n        <image :src=\"selectedImage\" mode=\"aspectFit\" class=\"preview-img\"></image>\n\n        <!-- 🔧 新增：诊断按钮 -->\n        <view class=\"button-group\">\n          <button @click=\"diagnoseFoodError\" :disabled=\"recognizing\" class=\"test-btn diagnostic\">\n            {{ recognizing ? '诊断中...' : '🔍 深度诊断错误' }}\n          </button>\n        </view>\n      </view>\n\n      <view v-if=\"recognitionResult\" class=\"result-box\">\n        <text class=\"result-title\">识别结果：</text>\n        \n        <!-- 🔧 优化后的统一食物识别结果 -->\n        <view v-if=\"recognitionResult.recognizedFoods && recognitionResult.recognizedFoods.length > 0\" class=\"result-section\">\n          <text class=\"result-subtitle\">🍽️ 识别到的食物：</text>\n          <view v-for=\"food in recognitionResult.recognizedFoods\" :key=\"food.name\" class=\"result-item\">\n            <text>{{ food.name }} ({{ Math.round(food.confidence * 100) }}% · {{ food.source }})</text>\n          </view>\n        </view>\n\n        <!-- 🔧 食物物体识别结果 -->\n        <view v-if=\"recognitionResult.foodObjects && recognitionResult.foodObjects.length > 0\" class=\"result-section\">\n          <text class=\"result-subtitle\">🔍 物体识别：</text>\n          <view v-for=\"obj in recognitionResult.foodObjects\" :key=\"obj.name\" class=\"result-item\">\n            <text>{{ obj.name }} ({{ Math.round(obj.confidence * 100) }}%)</text>\n          </view>\n        </view>\n\n        <!-- 🔧 兼容显示：旧格式菜品识别结果 -->\n        <view v-if=\"recognitionResult.dishResults && recognitionResult.dishResults.length > 0\" class=\"result-section\">\n          <text class=\"result-subtitle\">🍽️ 菜品识别（兼容）：</text>\n          <view v-for=\"dish in recognitionResult.dishResults\" :key=\"dish.name\" class=\"result-item\">\n            <text>{{ dish.name }} ({{ Math.round(dish.confidence * 100) }}%)</text>\n          </view>\n        </view>\n\n        <!-- 🔧 兼容显示：旧格式果蔬识别结果 -->\n        <view v-if=\"recognitionResult.ingredientResults && recognitionResult.ingredientResults.length > 0\" class=\"result-section\">\n          <text class=\"result-subtitle\">🥬 果蔬识别（兼容）：</text>\n          <view v-for=\"ingredient in recognitionResult.ingredientResults\" :key=\"ingredient.name\" class=\"result-item\">\n            <text>{{ ingredient.name }} ({{ Math.round(ingredient.confidence * 100) }}%)</text>\n          </view>\n        </view>\n\n        <!-- 图像理解结果 -->\n        <view v-if=\"recognitionResult.allKeywords && recognitionResult.allKeywords.length > 0\" class=\"result-section\">\n          <text class=\"result-subtitle\">🔍 关键词：</text>\n          <text class=\"keywords\">{{ recognitionResult.allKeywords.join('、') }}</text>\n        </view>\n\n        <!-- 识别摘要 -->\n        <view v-if=\"recognitionResult.summary\" class=\"result-section\">\n          <text class=\"result-subtitle\">📋 识别摘要：</text>\n          <text class=\"summary\">{{ recognitionResult.summary }}</text>\n        </view>\n      </view>\n\n      <view v-if=\"recognitionError\" class=\"error-box\">\n        <text class=\"error-title\">识别失败：</text>\n        <text class=\"error-msg\">{{ recognitionError }}</text>\n      </view>\n    </view>\n\n    <!-- 🔧 优化后的建议 -->\n    <view class=\"test-section\">\n      <view class=\"section-title\">💡 优化后的识别策略</view>\n      <view class=\"suggestions\">\n        <text class=\"suggestion\">✅ 已移除低效的菜品识别和果蔬识别API</text>\n        <text class=\"suggestion\">✅ 专注使用图像理解API进行食物识别</text>\n        <text class=\"suggestion\">✅ 增强了食物相关性过滤算法</text>\n        <text class=\"suggestion\">✅ 保留通用图像分析作为备用策略</text>\n        <text class=\"suggestion\">• 使用清晰、光线充足的食物图片</text>\n        <text class=\"suggestion\">• 确保食物在图片中占主要位置</text>\n        <text class=\"suggestion\">• 避免复杂背景干扰</text>\n        <text class=\"suggestion\">• 图像理解API对多种食物组合有更好的识别效果</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { quickDiagnosis } from '@/utils/ai/testBaiduAPI.js'\nimport { comprehensiveFoodRecognition } from '@/utils/ai/baiduImageService.js'\nimport { comprehensiveAPITest } from '@/utils/ai/realTestBaiduAPI.js'\n\nexport default {\n  data() {\n    return {\n      testing: false,\n      realTesting: false,\n      recognizing: false,\n      connectionResult: null,\n      realTestResult: null,\n      selectedImage: null,\n      recognitionResult: null,\n      recognitionError: null\n    }\n  },\n  \n  methods: {\n    // 测试API连接\n    async testConnection() {\n      this.testing = true\n      this.connectionResult = null\n      \n      try {\n        console.log('🧪 开始API连接测试...')\n        const result = await quickDiagnosis()\n        this.connectionResult = result\n        console.log('✅ 连接测试完成:', result)\n      } catch (error) {\n        console.error('❌ 连接测试失败:', error)\n        this.connectionResult = {\n          success: false,\n          issues: [`连接测试异常: ${error.message}`],\n          suggestions: []\n        }\n      } finally {\n        this.testing = false\n      }\n    },\n\n    // 实际API测试\n    async realAPITest() {\n      this.realTesting = true\n      this.realTestResult = null\n\n      try {\n        console.log('🧪 开始实际API测试...')\n        const result = await comprehensiveAPITest()\n        this.realTestResult = result\n        console.log('✅ 实际API测试完成:', result)\n\n        if (result.summary.successCount === result.summary.totalTests) {\n          uni.showToast({\n            title: '所有API测试通过',\n            icon: 'success'\n          })\n        } else {\n          uni.showToast({\n            title: `${result.summary.failureCount}个API测试失败`,\n            icon: 'error'\n          })\n        }\n      } catch (error) {\n        console.error('❌ 实际API测试失败:', error)\n        this.realTestResult = {\n          summary: {\n            successCount: 0,\n            totalTests: 3,\n            failureCount: 3,\n            issues: [`测试异常: ${error.message}`],\n            recommendations: ['请检查网络连接和代码配置']\n          }\n        }\n      } finally {\n        this.realTesting = false\n      }\n    },\n\n    // 🔧 新增：深度诊断食物识别错误\n    async diagnoseFoodError() {\n      console.log('🔍 开始深度诊断食物识别错误...')\n      this.recognizing = true\n      this.recognitionResult = null\n\n      try {\n        // 导入诊断函数\n        const { diagnoseFoodRecognitionError } = await import('../../utils/ai/baiduImageService.js')\n\n        // 使用当前选择的图片进行诊断\n        if (!this.selectedImage) {\n          uni.showToast({\n            title: '请先选择图片',\n            icon: 'error'\n          })\n          return\n        }\n\n        console.log('🔍 开始诊断，使用图片:', this.selectedImage)\n        const diagnosticResult = await diagnoseFoodRecognitionError(this.selectedImage)\n\n        console.log('🔍 诊断完成，结果:', diagnosticResult)\n\n        // 格式化诊断结果\n        this.recognitionResult = {\n          success: true,\n          data: {\n            recognizedFoods: [{\n              name: '诊断报告',\n              confidence: 100,\n              source: '深度诊断',\n              category: '系统分析'\n            }],\n            summary: `诊断完成：${diagnosticResult.analysis.summary}`,\n            confidence: 100,\n            diagnosticData: diagnosticResult\n          }\n        }\n\n        // 在控制台输出详细的诊断报告\n        console.log('📊 详细诊断报告:')\n        console.log('=' .repeat(80))\n        console.log('🔍 API调用状态:', diagnosticResult.analysis.apiStatus)\n        console.log('🔍 数据质量分析:', diagnosticResult.analysis.dataQuality)\n        console.log('🔍 发现的问题:', diagnosticResult.analysis.issues)\n        console.log('🔍 建议措施:', diagnosticResult.analysis.recommendations)\n        console.log('🔍 原始API响应:', diagnosticResult.rawResponses)\n        console.log('🔍 处理结果分析:', diagnosticResult.processedResults)\n        console.log('=' .repeat(80))\n\n        uni.showToast({\n          title: '诊断完成，请查看控制台',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('❌ 诊断过程异常:', error)\n        this.recognitionResult = {\n          success: false,\n          error: error.message,\n          data: {\n            summary: `诊断异常: ${error.message}`,\n            confidence: 0\n          }\n        }\n\n        uni.showToast({\n          title: '诊断失败',\n          icon: 'error'\n        })\n      } finally {\n        this.recognizing = false\n      }\n    },\n\n    // 选择图片进行识别测试\n    async selectImage() {\n      try {\n        const result = await uni.chooseImage({\n          count: 1,\n          sizeType: ['compressed'],\n          sourceType: ['album', 'camera']\n        })\n\n        if (result.tempFilePaths && result.tempFilePaths.length > 0) {\n          this.selectedImage = result.tempFilePaths[0]\n          this.recognitionResult = null\n          this.recognitionError = null\n          \n          // 自动开始识别\n          await this.recognizeImage(result.tempFilePaths[0])\n        }\n      } catch (error) {\n        console.error('❌ 选择图片失败:', error)\n        uni.showToast({\n          title: '选择图片失败',\n          icon: 'error'\n        })\n      }\n    },\n\n    // 识别图片\n    async recognizeImage(imagePath) {\n      this.recognizing = true\n      this.recognitionResult = null\n      this.recognitionError = null\n\n      try {\n        console.log('🚀 开始图片识别测试...')\n        const result = await comprehensiveFoodRecognition(imagePath)\n        \n        if (result.success) {\n          this.recognitionResult = result.data\n          console.log('✅ 识别成功:', result.data)\n          \n          uni.showToast({\n            title: '识别完成',\n            icon: 'success'\n          })\n        } else {\n          this.recognitionError = result.error || '识别失败'\n          console.error('❌ 识别失败:', result.error)\n          \n          uni.showToast({\n            title: '识别失败',\n            icon: 'error'\n          })\n        }\n      } catch (error) {\n        this.recognitionError = error.message || '识别过程异常'\n        console.error('❌ 识别异常:', error)\n        \n        uni.showToast({\n          title: '识别异常',\n          icon: 'error'\n        })\n      } finally {\n        this.recognizing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.subtitle {\n  font-size: 14px;\n  color: #666;\n  display: block;\n}\n\n.test-section {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.test-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-bottom: 15px;\n}\n\n.test-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-size: 16px;\n  flex: 1;\n  min-width: 140px;\n}\n\n.test-btn.secondary {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.test-btn.diagnostic {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\n}\n\n.test-btn:disabled {\n  opacity: 0.6;\n}\n\n.button-group {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.api-test-summary {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 15px;\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.test-stats {\n  font-weight: bold;\n  color: #495057;\n}\n\n.api-result {\n  margin-bottom: 12px;\n  padding: 10px;\n  background: white;\n  border-radius: 6px;\n  border-left: 3px solid #007bff;\n}\n\n.api-name {\n  font-weight: bold;\n  margin-right: 10px;\n  display: inline-block;\n}\n\n.error-detail {\n  display: block;\n  color: #dc3545;\n  font-size: 12px;\n  margin-top: 5px;\n  font-style: italic;\n}\n\n.recommendations {\n  margin-top: 15px;\n  background: #d4edda;\n  border-radius: 6px;\n  padding: 10px;\n}\n\n.rec-title {\n  font-weight: bold;\n  color: #155724;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.rec-item {\n  color: #155724;\n  font-size: 14px;\n  margin-bottom: 3px;\n  display: block;\n}\n\n.image-preview {\n  margin: 15px 0;\n  text-align: center;\n}\n\n.preview-img {\n  max-width: 100%;\n  max-height: 200px;\n  border-radius: 8px;\n}\n\n.result-box, .error-box {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 15px;\n  margin-top: 15px;\n}\n\n.result-title, .error-title {\n  font-weight: bold;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.success {\n  color: #28a745;\n}\n\n.error {\n  color: #dc3545;\n}\n\n.result-section {\n  margin-bottom: 15px;\n}\n\n.result-subtitle {\n  font-weight: bold;\n  color: #495057;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.result-item {\n  background: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  margin-bottom: 5px;\n  border-left: 3px solid #007bff;\n}\n\n.keywords, .summary {\n  background: white;\n  padding: 10px;\n  border-radius: 6px;\n  line-height: 1.5;\n  display: block;\n}\n\n.issues {\n  margin-top: 10px;\n}\n\n.issues-title {\n  font-weight: bold;\n  color: #dc3545;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.issue-item {\n  color: #dc3545;\n  font-size: 14px;\n  margin-bottom: 3px;\n  display: block;\n}\n\n.suggestions {\n  background: #e7f3ff;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.suggestion {\n  color: #0066cc;\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.error-msg {\n  color: #dc3545;\n  font-size: 14px;\n  line-height: 1.5;\n  display: block;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/2025077166-源代码/pz/pages/test/baidu-ai-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "quickDiagnosis", "comprehensiveAPITest", "comprehensiveFoodRecognition"], "mappings": ";;;;;AA4KA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,aAAa;AAAA,MACb,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,iBAAiB;AACrB,WAAK,UAAU;AACf,WAAK,mBAAmB;AAExB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,iBAAiB;AAC7B,cAAM,SAAS,MAAMC,qCAAe;AACpC,aAAK,mBAAmB;AACxBD,sBAAAA,MAAY,MAAA,OAAA,uCAAA,aAAa,MAAM;AAAA,MAC/B,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,KAAK;AAChC,aAAK,mBAAmB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ,CAAC,WAAW,MAAM,OAAO,EAAE;AAAA,UACnC,aAAa,CAAC;AAAA,QAChB;AAAA,MACF,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAEtB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,iBAAiB;AAC7B,cAAM,SAAS,MAAME,+CAAqB;AAC1C,aAAK,iBAAiB;AACtBF,sBAAAA,MAAA,MAAA,OAAA,uCAAY,gBAAgB,MAAM;AAElC,YAAI,OAAO,QAAQ,iBAAiB,OAAO,QAAQ,YAAY;AAC7DA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,GAAG,OAAO,QAAQ,YAAY;AAAA,YACrC,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,gBAAgB,KAAK;AACnC,aAAK,iBAAiB;AAAA,UACpB,SAAS;AAAA,YACP,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,QAAQ,CAAC,SAAS,MAAM,OAAO,EAAE;AAAA,YACjC,iBAAiB,CAAC,cAAc;AAAA,UAClC;AAAA,QACF;AAAA,MACF,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxBA,oBAAAA,MAAY,MAAA,OAAA,uCAAA,oBAAoB;AAChC,WAAK,cAAc;AACnB,WAAK,oBAAoB;AAEzB,UAAI;AAEF,cAAM,EAAE,iCAAiC,MAAa;AAGtD,YAAI,CAAC,KAAK,eAAe;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD;AAAA,QACF;AAEAA,sBAAY,MAAA,MAAA,OAAA,uCAAA,iBAAiB,KAAK,aAAa;AAC/C,cAAM,mBAAmB,MAAM,6BAA6B,KAAK,aAAa;AAE9EA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,eAAe,gBAAgB;AAG3C,aAAK,oBAAoB;AAAA,UACvB,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,iBAAiB,CAAC;AAAA,cAChB,MAAM;AAAA,cACN,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,UAAU;AAAA,YACZ,CAAC;AAAA,YACD,SAAS,QAAQ,iBAAiB,SAAS,OAAO;AAAA,YAClD,YAAY;AAAA,YACZ,gBAAgB;AAAA,UAClB;AAAA,QACF;AAGAA,sBAAAA,0DAAY,YAAY;AACxBA,sBAAY,MAAA,MAAA,OAAA,uCAAA,IAAK,OAAO,EAAE,CAAC;AAC3BA,sBAAY,MAAA,MAAA,OAAA,uCAAA,eAAe,iBAAiB,SAAS,SAAS;AAC9DA,sBAAY,MAAA,MAAA,OAAA,uCAAA,cAAc,iBAAiB,SAAS,WAAW;AAC/DA,sBAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,iBAAiB,SAAS,MAAM;AACzDA,sBAAA,MAAA,MAAA,OAAA,uCAAY,YAAY,iBAAiB,SAAS,eAAe;AACjEA,sBAAA,MAAA,MAAA,OAAA,uCAAY,eAAe,iBAAiB,YAAY;AACxDA,sBAAA,MAAA,MAAA,OAAA,uCAAY,cAAc,iBAAiB,gBAAgB;AAC3DA,sBAAY,MAAA,MAAA,OAAA,uCAAA,IAAK,OAAO,EAAE,CAAC;AAE3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MAED,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,KAAK;AAChC,aAAK,oBAAoB;AAAA,UACvB,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,UACb,MAAM;AAAA,YACJ,SAAS,SAAS,MAAM,OAAO;AAAA,YAC/B,YAAY;AAAA,UACd;AAAA,QACF;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,SAAS,MAAMA,cAAG,MAAC,YAAY;AAAA,UACnC,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,UACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,SAC/B;AAED,YAAI,OAAO,iBAAiB,OAAO,cAAc,SAAS,GAAG;AAC3D,eAAK,gBAAgB,OAAO,cAAc,CAAC;AAC3C,eAAK,oBAAoB;AACzB,eAAK,mBAAmB;AAGxB,gBAAM,KAAK,eAAe,OAAO,cAAc,CAAC,CAAC;AAAA,QACnD;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,eAAe,WAAW;AAC9B,WAAK,cAAc;AACnB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AAExB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,gBAAgB;AAC5B,cAAM,SAAS,MAAMG,2BAA4B,6BAAC,SAAS;AAE3D,YAAI,OAAO,SAAS;AAClB,eAAK,oBAAoB,OAAO;AAChCH,wBAAY,MAAA,MAAA,OAAA,uCAAA,WAAW,OAAO,IAAI;AAElCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,eACI;AACL,eAAK,mBAAmB,OAAO,SAAS;AACxCA,wBAAc,MAAA,MAAA,SAAA,uCAAA,WAAW,OAAO,KAAK;AAErCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACd,aAAK,mBAAmB,MAAM,WAAW;AACzCA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAE9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpYA,GAAG,WAAW,eAAe;"}
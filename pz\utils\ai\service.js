import { DEEPSEEK_BASE_URL, DEEPSEEK_API_KEY, AI_CONFIG } from './config.js';

class AIService {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }
  // 获取当前位置信息
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: MAP_CONFIG.LOCATION_TYPES.GCJ02,
        isHighAccuracy: true, // 开启高精度定位
        highAccuracyExpireTime: 3000,
        timeout: LOCATION_CONFIG.TIMEOUT,
        success: async (res) => {
          try {
            console.log('获取到的原始位置信息:', {
              latitude: res.latitude,
              longitude: res.longitude
            });

            const addressRes = await uni.request({
              url: MAP_CONFIG.GEOCODER_URL,
              method: 'GET',
              data: {
                location: `${res.latitude},${res.longitude}`,
                key: MAP_CONFIG.API_KEY,
                ...MAP_CONFIG.PARAMS
              }
            });

            console.log('腾讯地图API响应:', addressRes);

            if (addressRes.statusCode === 200 && addressRes.data.status === 0) {
              const addressInfo = addressRes.data.result;
              const addressComponent = addressInfo.address_component;

              const locationDetail = {
                latitude: res.latitude,
                longitude: res.longitude,
                address: addressInfo.formatted_addresses?.recommend || addressInfo.address,
                district: addressComponent.district,
                city: addressComponent.city,
                province: addressComponent.province,
                street: addressComponent.street,
                formatted_address: addressInfo.formatted_addresses?.recommend
              };

              console.log('解析后的位置详情:', locationDetail);
              resolve(locationDetail);
            } else {
              console.error('地图API错误:', addressRes.data);
              reject(new Error(LOCATION_CONFIG.ERROR_MESSAGES.GEOCODER_FAIL));
            }
          } catch (error) {
            console.error('地址解析错误:', error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error('获取位置失败:', error);
          reject(new Error(LOCATION_CONFIG.ERROR_MESSAGES.LOCATION_FAIL));
        }
      });
    });
  }

  // 发送消息到AI（带重试机制）
  async sendMessageWithRetry(message, maxRetries = AI_CONFIG.MAX_RETRIES) {
    let retries = 0;

    while (retries < maxRetries) {
      try {
        const response = await this.sendMessage(message);
        return response;
      } catch (error) {
        retries++;
        console.error(`AI服务调用失败 (${retries}/${maxRetries}):`, error);

        if (retries === maxRetries) {
          throw new Error(AI_CONFIG.ERROR_MESSAGES.REQUEST_FAIL);
        }

        // 指数退避重试
        const delay = Math.min(AI_CONFIG.RETRY_DELAY * Math.pow(2, retries) + Math.random() * 1000, 15000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 发送消息到AI
  async sendMessage(message) {
    try {
      const requestData = {
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的医疗助手，请用中文简洁地回答问题。'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        stream: false
      };

      const response = await uni.request({
        url: this.baseUrl,
        method: 'POST',
        data: requestData,
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'Accept': 'application/json'
        },
        timeout: AI_CONFIG.TIMEOUT,
        enableHttp2: true,
        enableQuic: true,
        enableCache: true
      });

      if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices[0]) {
        return response.data.choices[0].message.content;
      } else {
        throw new Error(`AI服务响应异常: ${response.statusCode}`);
      }
    } catch (error) {
      console.error('AI服务请求失败：', error);
      if (error.errMsg && error.errMsg.includes('timeout')) {
        throw new Error(AI_CONFIG.ERROR_MESSAGES.TIMEOUT);
      } else if (error.errMsg && error.errMsg.includes('fail')) {
        throw new Error(AI_CONFIG.ERROR_MESSAGES.NETWORK_ERROR);
      }
      throw error;
    }
  }
}

// 创建DeepSeek服务实例
const deepseekService = new AIService(DEEPSEEK_BASE_URL, DEEPSEEK_API_KEY);

// 导出发送消息方法
export async function sendMessageToAI(message) {
  try {
    return await deepseekService.sendMessageWithRetry(message);
  } catch (error) {
    console.error('调用AI服务失败:', error);
    throw error;
  }
}

// 导出获取位置并发送到AI的方法
export async function sendLocationBasedMessage(message) {
  try {
    const location = await deepseekService.getCurrentLocation();
    const locationMessage = `基于以下位置信息：
当前位置：${location.formatted_address || location.address}
省份：${location.province}
城市：${location.city}
区域：${location.district}
街道：${location.street}
经度：${location.longitude}
纬度：${location.latitude}

${message}`;

    return await deepseekService.sendMessageWithRetry(locationMessage);
  } catch (error) {
    console.error('获取位置信息失败:', error);
    throw error;
  }
}
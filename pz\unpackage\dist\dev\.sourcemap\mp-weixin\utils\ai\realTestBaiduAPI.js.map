{"version": 3, "file": "realTestBaiduAPI.js", "sources": ["utils/ai/realTestBaiduAPI.js"], "sourcesContent": ["// 实际百度AI API测试脚本\n// 用于真实环境下的API调用测试和问题诊断\n\nimport { getBaiduAccessToken, BAIDU_CONFIG } from './baiduImageService.js'\n\n/**\n * 创建测试用的base64图片数据\n * 这是一个1x1像素的透明PNG图片的base64编码，用于API连接测试\n */\nconst TEST_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n\n/**\n * 实际测试百度菜品识别API\n */\nexport async function realTestDishAPI() {\n  console.log('🧪 开始实际测试百度菜品识别API...')\n\n  try {\n    // 1. 获取Access Token\n    console.log('🔑 获取Access Token...')\n    const accessToken = await getBaiduAccessToken()\n    console.log('✅ Access Token获取成功，长度:', accessToken.length)\n\n    // 2. 准备请求参数\n    const requestData = {\n      image: TEST_IMAGE_BASE64,\n      top_num: 10,\n      filter_threshold: 0.1,\n      baike_num: 5\n    }\n\n    console.log('📋 请求参数:')\n    console.log('  URL:', `${BAIDU_CONFIG.dishDetectUrl}?access_token=${accessToken.substring(0, 10)}...`)\n    console.log('  top_num:', requestData.top_num)\n    console.log('  filter_threshold:', requestData.filter_threshold)\n    console.log('  baike_num:', requestData.baike_num)\n    console.log('  image base64长度:', requestData.image.length)\n\n    // 3. 发送API请求\n    console.log('🚀 发送API请求...')\n    const response = await uni.request({\n      url: `${BAIDU_CONFIG.dishDetectUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 20000\n    })\n\n    // 4. 分析响应结果\n    console.log('📊 API响应分析:')\n    console.log('  HTTP状态码:', response.statusCode)\n    console.log('  响应头:', JSON.stringify(response.header, null, 2))\n    console.log('  完整响应数据:', JSON.stringify(response.data, null, 2))\n\n    if (response.statusCode === 200) {\n      const result = response.data\n\n      if (result.error_code) {\n        console.log('❌ API返回错误:')\n        console.log('  错误码:', result.error_code)\n        console.log('  错误信息:', result.error_msg)\n\n        // 分析常见错误\n        switch (result.error_code) {\n          case 17:\n            console.log('💡 诊断: 每日请求量超限制')\n            break\n          case 18:\n            console.log('💡 诊断: QPS超限制，请求过于频繁')\n            break\n          case 100:\n            console.log('💡 诊断: 无效参数')\n            break\n          case 110:\n            console.log('💡 诊断: Access Token无效')\n            break\n          case 111:\n            console.log('💡 诊断: Access Token过期')\n            break\n          case 216201:\n            console.log('💡 诊断: 图片中未检测到菜品')\n            break\n          default:\n            console.log('💡 诊断: 未知错误，请查看百度AI文档')\n        }\n\n        return {\n          success: false,\n          error: `API错误 ${result.error_code}: ${result.error_msg}`,\n          errorCode: result.error_code,\n          rawResponse: result\n        }\n      } else {\n        console.log('✅ API调用成功')\n        console.log('  识别结果数量:', result.result ? result.result.length : 0)\n\n        if (result.result && result.result.length > 0) {\n          console.log('  识别结果详情:')\n          result.result.forEach((item, index) => {\n            console.log(`    ${index + 1}. ${item.name} (置信度: ${item.probability})`)\n          })\n        }\n\n        return {\n          success: true,\n          data: result,\n          resultCount: result.result ? result.result.length : 0\n        }\n      }\n    } else {\n      console.log('❌ HTTP请求失败')\n      return {\n        success: false,\n        error: `HTTP ${response.statusCode}`,\n        rawResponse: response\n      }\n    }\n\n  } catch (error) {\n    console.error('❌ 测试过程异常:', error)\n    return {\n      success: false,\n      error: error.message,\n      exception: error\n    }\n  }\n}\n\n/**\n * 实际测试百度果蔬识别API\n */\nexport async function realTestIngredientAPI() {\n  console.log('🧪 开始实际测试百度果蔬识别API...')\n\n  try {\n    const accessToken = await getBaiduAccessToken()\n\n    const requestData = {\n      image: TEST_IMAGE_BASE64,\n      top_num: 8\n    }\n\n    console.log('📋 果蔬识别请求参数:')\n    console.log('  URL:', `${BAIDU_CONFIG.ingredientUrl}?access_token=${accessToken.substring(0, 10)}...`)\n    console.log('  top_num:', requestData.top_num)\n\n    const response = await uni.request({\n      url: `${BAIDU_CONFIG.ingredientUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 20000\n    })\n\n    console.log('📊 果蔬识别API响应:')\n    console.log('  HTTP状态码:', response.statusCode)\n    console.log('  完整响应:', JSON.stringify(response.data, null, 2))\n\n    if (response.statusCode === 200 && response.data) {\n      if (response.data.error_code) {\n        return {\n          success: false,\n          error: `果蔬识别API错误 ${response.data.error_code}: ${response.data.error_msg}`,\n          errorCode: response.data.error_code\n        }\n      } else {\n        return {\n          success: true,\n          data: response.data,\n          resultCount: response.data.result ? response.data.result.length : 0\n        }\n      }\n    } else {\n      return {\n        success: false,\n        error: `果蔬识别HTTP ${response.statusCode}`\n      }\n    }\n\n  } catch (error) {\n    console.error('❌ 果蔬识别测试异常:', error)\n    return {\n      success: false,\n      error: error.message\n    }\n  }\n}\n\n/**\n * 实际测试百度图像理解API\n */\nexport async function realTestImageUnderstandingAPI() {\n  console.log('🧪 开始实际测试百度图像理解API...')\n\n  try {\n    const accessToken = await getBaiduAccessToken()\n\n    // 🔧 使用修复后的参数格式\n    const requestData = {\n      image: TEST_IMAGE_BASE64,\n      scenes: 'food', // 修复：使用字符串而不是数组\n      feature: 'keyword,tag', // 修复：使用逗号分隔的字符串\n      question: '图片中有什么食物？' // 简化问题\n    }\n\n    console.log('📋 图像理解请求参数:')\n    console.log('  URL:', `${BAIDU_CONFIG.imageUnderstandingRequestUrl}?access_token=${accessToken.substring(0, 10)}...`)\n    console.log('  scenes:', requestData.scenes)\n    console.log('  feature:', requestData.feature)\n    console.log('  question:', requestData.question)\n\n    // 第一步：提交请求\n    const requestResponse = await uni.request({\n      url: `${BAIDU_CONFIG.imageUnderstandingRequestUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 20000\n    })\n\n    console.log('📊 图像理解请求响应:')\n    console.log('  HTTP状态码:', requestResponse.statusCode)\n    console.log('  响应数据:', JSON.stringify(requestResponse.data, null, 2))\n\n    if (requestResponse.statusCode === 200 && requestResponse.data) {\n      if (requestResponse.data.error_code) {\n        return {\n          success: false,\n          error: `图像理解请求错误 ${requestResponse.data.error_code}: ${requestResponse.data.error_msg}`,\n          errorCode: requestResponse.data.error_code\n        }\n      } else if (requestResponse.data.task_id) {\n        console.log('✅ 图像理解任务提交成功，任务ID:', requestResponse.data.task_id)\n        return {\n          success: true,\n          taskId: requestResponse.data.task_id,\n          message: '任务提交成功，需要轮询获取结果'\n        }\n      } else {\n        return {\n          success: false,\n          error: '图像理解请求失败：未获取到任务ID'\n        }\n      }\n    } else {\n      return {\n        success: false,\n        error: `图像理解HTTP ${requestResponse.statusCode}`\n      }\n    }\n\n  } catch (error) {\n    console.error('❌ 图像理解测试异常:', error)\n    return {\n      success: false,\n      error: error.message\n    }\n  }\n}\n\n/**\n * 综合API测试\n */\nexport async function comprehensiveAPITest() {\n  console.log('🚀 开始综合API测试...')\n\n  const testResults = {\n    timestamp: new Date().toISOString(),\n    dishAPI: null,\n    ingredientAPI: null,\n    imageUnderstandingAPI: null,\n    summary: {\n      totalTests: 3,\n      successCount: 0,\n      failureCount: 0,\n      issues: [],\n      recommendations: []\n    }\n  }\n\n  // 测试菜品识别API\n  console.log('\\n1️⃣ 测试菜品识别API')\n  testResults.dishAPI = await realTestDishAPI()\n  if (testResults.dishAPI.success) {\n    testResults.summary.successCount++\n  } else {\n    testResults.summary.failureCount++\n    testResults.summary.issues.push(`菜品识别: ${testResults.dishAPI.error}`)\n  }\n\n  // 测试果蔬识别API\n  console.log('\\n2️⃣ 测试果蔬识别API')\n  testResults.ingredientAPI = await realTestIngredientAPI()\n  if (testResults.ingredientAPI.success) {\n    testResults.summary.successCount++\n  } else {\n    testResults.summary.failureCount++\n    testResults.summary.issues.push(`果蔬识别: ${testResults.ingredientAPI.error}`)\n  }\n\n  // 测试图像理解API\n  console.log('\\n3️⃣ 测试图像理解API')\n  testResults.imageUnderstandingAPI = await realTestImageUnderstandingAPI()\n  if (testResults.imageUnderstandingAPI.success) {\n    testResults.summary.successCount++\n  } else {\n    testResults.summary.failureCount++\n    testResults.summary.issues.push(`图像理解: ${testResults.imageUnderstandingAPI.error}`)\n  }\n\n  // 生成建议\n  if (testResults.summary.successCount === 3) {\n    testResults.summary.recommendations.push('✅ 所有API测试通过，可以进行实际图片测试')\n  } else if (testResults.summary.successCount > 0) {\n    testResults.summary.recommendations.push('⚠️ 部分API可用，建议检查失败的API配置')\n  } else {\n    testResults.summary.recommendations.push('❌ 所有API测试失败，请检查网络连接和API密钥')\n  }\n\n  // 输出测试报告\n  console.log('\\n📊 综合测试报告')\n  console.log('='.repeat(50))\n  console.log(`测试时间: ${testResults.timestamp}`)\n  console.log(`成功: ${testResults.summary.successCount}/${testResults.summary.totalTests}`)\n  console.log(`失败: ${testResults.summary.failureCount}/${testResults.summary.totalTests}`)\n\n  if (testResults.summary.issues.length > 0) {\n    console.log('\\n❌ 发现问题:')\n    testResults.summary.issues.forEach(issue => console.log(`  ${issue}`))\n  }\n\n  console.log('\\n💡 建议:')\n  testResults.summary.recommendations.forEach(rec => console.log(`  ${rec}`))\n  console.log('='.repeat(50))\n\n  return testResults\n}\n"], "names": ["uni", "getBaiduAccessToken", "BAIDU_CONFIG"], "mappings": ";;;AASA,MAAM,oBAAoB;AAKnB,eAAe,kBAAkB;AACtCA,gBAAAA,MAAY,MAAA,OAAA,sCAAA,uBAAuB;AAEnC,MAAI;AAEFA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,sBAAsB;AAClC,UAAM,cAAc,MAAMC,+CAAqB;AAC/CD,kBAAA,MAAA,MAAA,OAAA,sCAAY,0BAA0B,YAAY,MAAM;AAGxD,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,WAAW;AAAA,IACZ;AAEDA,kBAAAA,MAAA,MAAA,OAAA,sCAAY,UAAU;AACtBA,kBAAA,MAAA,MAAA,OAAA,sCAAY,UAAU,GAAGE,2BAAY,aAAC,aAAa,iBAAiB,YAAY,UAAU,GAAG,EAAE,CAAC,KAAK;AACrGF,kBAAY,MAAA,MAAA,OAAA,sCAAA,cAAc,YAAY,OAAO;AAC7CA,kBAAY,MAAA,MAAA,OAAA,sCAAA,uBAAuB,YAAY,gBAAgB;AAC/DA,2EAAY,gBAAgB,YAAY,SAAS;AACjDA,kBAAA,MAAA,MAAA,OAAA,sCAAY,qBAAqB,YAAY,MAAM,MAAM;AAGzDA,kBAAAA,yDAAY,eAAe;AAC3B,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK,GAAGE,2BAAY,aAAC,aAAa,iBAAiB,WAAW;AAAA,MAC9D,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAGDF,kBAAAA,MAAY,MAAA,OAAA,sCAAA,aAAa;AACzBA,kBAAY,MAAA,MAAA,OAAA,sCAAA,cAAc,SAAS,UAAU;AAC7CA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,UAAU,KAAK,UAAU,SAAS,QAAQ,MAAM,CAAC,CAAC;AAC9DA,kBAAAA,MAAY,MAAA,OAAA,sCAAA,aAAa,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC,CAAC;AAE/D,QAAI,SAAS,eAAe,KAAK;AAC/B,YAAM,SAAS,SAAS;AAExB,UAAI,OAAO,YAAY;AACrBA,sBAAAA,yDAAY,YAAY;AACxBA,sBAAY,MAAA,MAAA,OAAA,sCAAA,UAAU,OAAO,UAAU;AACvCA,sBAAY,MAAA,MAAA,OAAA,sCAAA,WAAW,OAAO,SAAS;AAGvC,gBAAQ,OAAO,YAAU;AAAA,UACvB,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,iBAAiB;AAC7B;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,sBAAsB;AAClC;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,aAAa;AACzB;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,uBAAuB;AACnC;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,uBAAuB;AACnC;AAAA,UACF,KAAK;AACHA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,kBAAkB;AAC9B;AAAA,UACF;AACEA,0BAAAA,MAAA,MAAA,OAAA,sCAAY,uBAAuB;AAAA,QACtC;AAED,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,SAAS,OAAO,UAAU,KAAK,OAAO,SAAS;AAAA,UACtD,WAAW,OAAO;AAAA,UAClB,aAAa;AAAA,QACd;AAAA,MACT,OAAa;AACLA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,WAAW;AACvBA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,aAAa,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC;AAEjE,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAC7CA,wBAAAA,MAAA,MAAA,OAAA,uCAAY,WAAW;AACvB,iBAAO,OAAO,QAAQ,CAAC,MAAM,UAAU;AACrCA,0BAAY,MAAA,MAAA,OAAA,uCAAA,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI,UAAU,KAAK,WAAW,GAAG;AAAA,UACnF,CAAW;AAAA,QACF;AAED,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN,aAAa,OAAO,SAAS,OAAO,OAAO,SAAS;AAAA,QACrD;AAAA,MACF;AAAA,IACP,OAAW;AACLA,oBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY;AACxB,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,QAAQ,SAAS,UAAU;AAAA,QAClC,aAAa;AAAA,MACd;AAAA,IACF;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAChC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,MACb,WAAW;AAAA,IACZ;AAAA,EACF;AACH;AAKO,eAAe,wBAAwB;AAC5CA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,uBAAuB;AAEnC,MAAI;AACF,UAAM,cAAc,MAAMC,+CAAqB;AAE/C,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,SAAS;AAAA,IACV;AAEDD,kBAAAA,0DAAY,cAAc;AAC1BA,kBAAA,MAAA,MAAA,OAAA,uCAAY,UAAU,GAAGE,2BAAY,aAAC,aAAa,iBAAiB,YAAY,UAAU,GAAG,EAAE,CAAC,KAAK;AACrGF,kBAAY,MAAA,MAAA,OAAA,uCAAA,cAAc,YAAY,OAAO;AAE7C,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK,GAAGE,2BAAY,aAAC,aAAa,iBAAiB,WAAW;AAAA,MAC9D,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAEDF,kBAAAA,0DAAY,eAAe;AAC3BA,kBAAY,MAAA,MAAA,OAAA,uCAAA,cAAc,SAAS,UAAU;AAC7CA,kBAAAA,MAAY,MAAA,OAAA,uCAAA,WAAW,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC,CAAC;AAE7D,QAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAChD,UAAI,SAAS,KAAK,YAAY;AAC5B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,aAAa,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,SAAS;AAAA,UACxE,WAAW,SAAS,KAAK;AAAA,QAC1B;AAAA,MACT,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,UACf,aAAa,SAAS,KAAK,SAAS,SAAS,KAAK,OAAO,SAAS;AAAA,QACnE;AAAA,MACF;AAAA,IACP,OAAW;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,SAAS,UAAU;AAAA,MACvC;AAAA,IACF;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd;AAAA,EACF;AACH;AAKO,eAAe,gCAAgC;AACpDA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,uBAAuB;AAEnC,MAAI;AACF,UAAM,cAAc,MAAMC,+CAAqB;AAG/C,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,QAAQ;AAAA;AAAA,MACR,SAAS;AAAA;AAAA,MACT,UAAU;AAAA;AAAA,IACX;AAEDD,kBAAAA,0DAAY,cAAc;AAC1BA,kBAAY,MAAA,MAAA,OAAA,uCAAA,UAAU,GAAGE,2BAAY,aAAC,4BAA4B,iBAAiB,YAAY,UAAU,GAAG,EAAE,CAAC,KAAK;AACpHF,kBAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,YAAY,MAAM;AAC3CA,kBAAY,MAAA,MAAA,OAAA,uCAAA,cAAc,YAAY,OAAO;AAC7CA,kBAAY,MAAA,MAAA,OAAA,uCAAA,eAAe,YAAY,QAAQ;AAG/C,UAAM,kBAAkB,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACxC,KAAK,GAAGE,2BAAY,aAAC,4BAA4B,iBAAiB,WAAW;AAAA,MAC7E,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAEDF,kBAAAA,0DAAY,cAAc;AAC1BA,kBAAA,MAAA,MAAA,OAAA,uCAAY,cAAc,gBAAgB,UAAU;AACpDA,kBAAAA,MAAA,MAAA,OAAA,uCAAY,WAAW,KAAK,UAAU,gBAAgB,MAAM,MAAM,CAAC,CAAC;AAEpE,QAAI,gBAAgB,eAAe,OAAO,gBAAgB,MAAM;AAC9D,UAAI,gBAAgB,KAAK,YAAY;AACnC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,YAAY,gBAAgB,KAAK,UAAU,KAAK,gBAAgB,KAAK,SAAS;AAAA,UACrF,WAAW,gBAAgB,KAAK;AAAA,QACjC;AAAA,MACT,WAAiB,gBAAgB,KAAK,SAAS;AACvCA,sBAAY,MAAA,MAAA,OAAA,uCAAA,sBAAsB,gBAAgB,KAAK,OAAO;AAC9D,eAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ,gBAAgB,KAAK;AAAA,UAC7B,SAAS;AAAA,QACV;AAAA,MACT,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,MACF;AAAA,IACP,OAAW;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,gBAAgB,UAAU;AAAA,MAC9C;AAAA,IACF;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACd;AAAA,EACF;AACH;AAKO,eAAe,uBAAuB;AAC3CA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,iBAAiB;AAE7B,QAAM,cAAc;AAAA,IAClB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,SAAS;AAAA,IACT,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,QAAQ,CAAE;AAAA,MACV,iBAAiB,CAAE;AAAA,IACpB;AAAA,EACF;AAGDA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,iBAAiB;AAC7B,cAAY,UAAU,MAAM,gBAAiB;AAC7C,MAAI,YAAY,QAAQ,SAAS;AAC/B,gBAAY,QAAQ;AAAA,EACxB,OAAS;AACL,gBAAY,QAAQ;AACpB,gBAAY,QAAQ,OAAO,KAAK,SAAS,YAAY,QAAQ,KAAK,EAAE;AAAA,EACrE;AAGDA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,iBAAiB;AAC7B,cAAY,gBAAgB,MAAM,sBAAuB;AACzD,MAAI,YAAY,cAAc,SAAS;AACrC,gBAAY,QAAQ;AAAA,EACxB,OAAS;AACL,gBAAY,QAAQ;AACpB,gBAAY,QAAQ,OAAO,KAAK,SAAS,YAAY,cAAc,KAAK,EAAE;AAAA,EAC3E;AAGDA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,iBAAiB;AAC7B,cAAY,wBAAwB,MAAM,8BAA+B;AACzE,MAAI,YAAY,sBAAsB,SAAS;AAC7C,gBAAY,QAAQ;AAAA,EACxB,OAAS;AACL,gBAAY,QAAQ;AACpB,gBAAY,QAAQ,OAAO,KAAK,SAAS,YAAY,sBAAsB,KAAK,EAAE;AAAA,EACnF;AAGD,MAAI,YAAY,QAAQ,iBAAiB,GAAG;AAC1C,gBAAY,QAAQ,gBAAgB,KAAK,wBAAwB;AAAA,EAClE,WAAU,YAAY,QAAQ,eAAe,GAAG;AAC/C,gBAAY,QAAQ,gBAAgB,KAAK,yBAAyB;AAAA,EACtE,OAAS;AACL,gBAAY,QAAQ,gBAAgB,KAAK,2BAA2B;AAAA,EACrE;AAGDA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,aAAa;AACzBA,gBAAY,MAAA,MAAA,OAAA,uCAAA,IAAI,OAAO,EAAE,CAAC;AAC1BA,sBAAA,MAAA,OAAA,uCAAY,SAAS,YAAY,SAAS,EAAE;AAC5CA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,OAAO,YAAY,QAAQ,YAAY,IAAI,YAAY,QAAQ,UAAU,EAAE;AACvFA,gBAAAA,MAAY,MAAA,OAAA,uCAAA,OAAO,YAAY,QAAQ,YAAY,IAAI,YAAY,QAAQ,UAAU,EAAE;AAEvF,MAAI,YAAY,QAAQ,OAAO,SAAS,GAAG;AACzCA,kBAAAA,MAAA,MAAA,OAAA,uCAAY,WAAW;AACvB,gBAAY,QAAQ,OAAO,QAAQ,WAASA,cAAA,MAAA,MAAA,OAAA,uCAAY,KAAK,KAAK,EAAE,CAAC;AAAA,EACtE;AAEDA,gBAAAA,MAAA,MAAA,OAAA,uCAAY,UAAU;AACtB,cAAY,QAAQ,gBAAgB,QAAQ,SAAOA,cAAA,MAAA,MAAA,OAAA,uCAAY,KAAK,GAAG,EAAE,CAAC;AAC1EA,gBAAY,MAAA,MAAA,OAAA,uCAAA,IAAI,OAAO,EAAE,CAAC;AAE1B,SAAO;AACT;;"}
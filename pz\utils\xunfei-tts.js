import CryptoJS from 'crypto-js'
// 科大讯飞语音合成工具类（微信小程序版本）

// 科大讯飞TTS配置
const XUNFEI_CONFIG = {
	APPID: 'a664abcb',
	API_SECRET: 'YjRjM2QyYzVjNzNiZTMzYmEwM2EyOGM3',
	API_KEY: '46faab98c7cc744e346baa6115c74886',
	HOST_URL: 'wss://tts-api.xfyun.cn/v2/tts'
}

// Base64编码表
const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

// 简单的Base64编码函数（兼容微信小程序）
function base64Encode(str) {
	let result = ''
	let i = 0
	const bytes = []

	// 转换为UTF-8字节
	for (let j = 0; j < str.length; j++) {
		const code = str.charCodeAt(j)
		if (code < 0x80) {
			bytes.push(code)
		} else if (code < 0x800) {
			bytes.push(0xc0 | (code >> 6))
			bytes.push(0x80 | (code & 0x3f))
		} else {
			bytes.push(0xe0 | (code >> 12))
			bytes.push(0x80 | ((code >> 6) & 0x3f))
			bytes.push(0x80 | (code & 0x3f))
		}
	}

	// Base64编码
	while (i < bytes.length) {
		const a = bytes[i++]
		const b = i < bytes.length ? bytes[i++] : 0
		const c = i < bytes.length ? bytes[i++] : 0

		const bitmap = (a << 16) | (b << 8) | c

		result += base64Chars.charAt((bitmap >> 18) & 63)
		result += base64Chars.charAt((bitmap >> 12) & 63)
		result += i - 2 < bytes.length ? base64Chars.charAt((bitmap >> 6) & 63) : '='
		result += i - 1 < bytes.length ? base64Chars.charAt(bitmap & 63) : '='
	}

	return result
}

// 简单的HMAC-SHA256实现（适用于微信小程序）
function hmacSha256(message, secret) {
	// 由于微信小程序环境限制，这里使用简化版本
	// 实际项目中建议使用后端代理或官方SDK
	const combined = message + secret
	let hash = 0
	for (let i = 0; i < combined.length; i++) {
		const char = combined.charCodeAt(i)
		hash = ((hash << 5) - hash) + char
		hash = hash & hash // 转换为32位整数
	}
	return Math.abs(hash).toString(16).padStart(32, '0')
}

class XunfeiTTS {
	constructor() {
		this.socketTask = null
		this.isConnected = false
		this.audioData = []
		this.currentCallback = null
	}

	/**
	 * 文本转语音
	 * @param {string} text 要转换的文本
	 * @returns {Promise<string>} 返回音频Base64数据
	 */
	async textToSpeech(text) {
		return new Promise((resolve, reject) => {
			try {
				this.currentCallback = {
					resolve,
					reject
				}
				this.audioData = []

				// 构建WebSocket连接URL
				const wsUrl = this.buildWebSocketUrl()

				// 创建WebSocket连接
				this.socketTask = uni.connectSocket({
					url: wsUrl,
					protocols: ['chat']
				})

				this.socketTask.onOpen(() => {
					console.log('科大讯飞TTS WebSocket连接成功')
					this.isConnected = true

					// 发送音频合成请求
					this.sendTTSRequest(text)
				})

				this.socketTask.onMessage((res) => {
					this.handleMessage(res.data)
				})

				this.socketTask.onError((error) => {
					console.error('科大讯飞TTS WebSocket错误:', error)
					this.isConnected = false
					reject(new Error('WebSocket连接失败'))
				})

				this.socketTask.onClose(() => {
					console.log('科大讯飞TTS WebSocket连接关闭')
					this.isConnected = false
				})

			} catch (error) {
				console.error('科大讯飞TTS初始化失败:', error)
				reject(error)
			}
		})
	}
	getSpeechTotextUrl(options = {}) {
		const ts = Math.floor(new Date().getTime() / 1000);
		const baseString = XUNFEI_CONFIG.APPID + ts;

		// 第一步：MD5(appid + ts)
		const md5Str = CryptoJS.MD5(baseString).toString();

		// 第二步：HmacSHA1签名（key=apiKey）
		const hmacSha1 = CryptoJS.HmacSHA1(md5Str, '8e01d98f8386345639f72bd276c9e3cb');
		let signa = CryptoJS.enc.Base64.stringify(hmacSha1)
		// 第三步：Base64 编码 + URI 编码
		signa = encodeURIComponent(signa);
		console.log(md5Str, '8e01d98f8386345639f72bd276c9e3cb', hmacSha1, signa)
		// 构造基本参数
		const baseParams = {
			appid: XUNFEI_CONFIG.APPID,
			ts,
			signa,
			...options, // 附加参数，如 pd=edu, lang=cn 等
		};

		// 拼接成 URL 查询参数字符串
		const queryParams = Object.keys(baseParams)
			.map(key => `${(key)}=${(baseParams[key])}`)
			.join('&');

		return `ws://rtasr.xfyun.cn/v1/ws?${queryParams}`;
	}
	generateSigna(appId, secretKey, ts) {
		const baseString = appId + ts
		const md5Hash = CryptoJS.MD5(baseString).toString()
		const hmacSha1 = CryptoJS.HmacSHA1(md5Hash, secretKey)
		return CryptoJS.enc.Base64.stringify(hmacSha1)
	}
	async uploadToXunfei(filePath, fileName, fileSize, duration = 10) {
		try {
			console.log('=== 开始上传音频文件 ===')
			console.log('参数检查:', {
				filePath: filePath,
				fileName: fileName,
				fileSize: fileSize,
				duration: duration,
				filePathType: typeof filePath,
				fileNameType: typeof fileName,
				fileSizeType: typeof fileSize,
				durationType: typeof duration
			})

			// 参数验证
			if (!filePath || filePath === 'undefined') {
				throw new Error('文件路径无效: ' + filePath)
			}
			if (!fileName || fileName === 'undefined') {
				throw new Error('文件名无效: ' + fileName)
			}
			if (!fileSize || fileSize === 'undefined' || isNaN(fileSize)) {
				console.warn('文件大小无效，使用默认值:', fileSize)
				fileSize = 1024
			}
			if (!duration || duration === 'undefined' || isNaN(duration)) {
				console.warn('时长无效，使用默认值:', duration)
				duration = 10
			}

			const ts = Math.floor(Date.now() / 1000).toString()
			const signa = this.generateSigna(XUNFEI_CONFIG.APPID, XUNFEI_CONFIG.API_SECRET, ts)

			const uploadUrl = `https://raasr.xfyun.cn/v2/api/upload?` +
				`duration=${duration}&fileName=${encodeURIComponent(fileName)}&fileSize=${fileSize}` +
				`&signa=${encodeURIComponent(signa)}&appId=${XUNFEI_CONFIG.APPID}&ts=${ts}`

			console.log('上传URL:', uploadUrl)

			return new Promise((resolve, reject) => {
				uni.uploadFile({
					url: uploadUrl,
					filePath: filePath,
					name: 'file',
					header: {
						'Content-Type': 'application/octet-stream'
					},
					success: (res) => {
						try {
							console.log('上传响应原始数据:', res)
							const data = JSON.parse(res.data)
							console.log('解析后的响应数据:', data)

							if (data.ok === 0) {
								console.log('✅ 上传成功，订单ID：', data.data.orderId)
								resolve(data.data.orderId)
							} else {
								console.error('❌ 上传失败：', data)
								reject(new Error(`上传失败: ${data.desc || data.message || '未知错误'}`))
							}
						} catch (parseError) {
							console.error('❌ 解析响应数据失败:', parseError)
							console.error('原始响应数据:', res.data)
							reject(new Error('解析响应失败: ' + parseError.message))
						}
					},
					fail: (err) => {
						console.error('❌ 上传请求失败：', err)
						reject(new Error(`网络请求失败: ${err.errMsg || err.message || '未知错误'}`))
					}
				})
			})
		} catch (error) {
			console.error('❌ 上传准备阶段失败:', error)
			throw new Error('上传准备失败: ' + error.message)
		}
	}
	getWebSocketUrl() {
		const host = 'tts-api.xfyun.cn'
		const date = new Date().toUTCString()
		const algorithm = 'hmac-sha256'
		const headers = 'host date request-line'
		const signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`
		const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, XUNFEI_CONFIG.API_SECRET)
		const signature = CryptoJS.enc.Base64.stringify(signatureSha)
		const authorizationOrigin =
			`api_key="${XUNFEI_CONFIG.API_KEY}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`
		const authorization = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(authorizationOrigin)) // ✅ 兼容


		return `wss://tts-api.xfyun.cn/v2/tts?authorization=${authorization}&date=${date}&host=${host}`
	}
	encodeText(text) {
		return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(text))
	}

	// websocket流式获取base64
	connectWebSocketAsPromise(text = "无") {
		return new Promise((resolve, reject) => {
			const wsUrl = this.getWebSocketUrl()
			console.log('wsUrl', wsUrl)
			this.btnStatus = 'CONNECTING'
			this.audioBuffer = []

			// 建立连接
			const socketTask = uni.connectSocket({
				url: wsUrl,
				success: () => console.log('WebSocket 连接发起成功'),
				fail: (err) => {
					console.error('WebSocket 建立失败', err)
					reject(err)
				}
			})

			// 监听 open
			socketTask.onOpen(() => {
				console.log('WebSocket 已打开')

				const params = {
					common: {
						app_id: XUNFEI_CONFIG.APPID
					},
					business: {
						aue: 'lame',
						sfl: 1,
						auf: 'audio/L16;rate=16000',
						vcn: 'x4_aisxping',
						speed: 55,
						volume: 60,
						pitch: 50,
						bgs: 0,
						tte: 'UTF8'
					},
					data: {
						status: 2,
						text: this.encodeText(text)
					}
				}

				socketTask.send({
					data: JSON.stringify(params),
					fail: (err) => {
						console.error('消息发送失败', err)
						reject(err)
					}
				})
			})

			// 监听消息
			socketTask.onMessage((res) => {
				const json = JSON.parse(res.data)

				if (json.code !== 0) {
					console.error('讯飞返回错误:', json)
					this.btnStatus = 'UNDEFINED'
					socketTask.close()
					reject(json)
					return
				}

				const audio = json.data.audio
				const isLast = json.data.status === 2
				this.audioBuffer.push(audio)

				if (isLast) {
					console.log('接收完毕，关闭 WebSocket')
					socketTask.close()
					this.btnStatus = 'STOP'
					resolve(this.audioBuffer) // ✅ 解析最终音频数据
				}
			})

			// 监听错误
			socketTask.onError((err) => {
				console.error('WebSocket 错误', err)
				this.btnStatus = 'UNDEFINED'
				reject(err)
			})

			// 监听关闭
			socketTask.onClose(() => {
				console.log('WebSocket 已关闭')
			})
		})
	}



	// 文本识别加密函数
	/**
	 * 生成讯飞OCR接口签名所需的参数
	 * @param {string} apiKey - 控制台获取的APIKey
	 * @param {string} apiSecret - 控制台获取的APISecret
	 * @param {string} urlPath - 请求路径，例如 /v1/private/se75ocrbm
	 * @param {string} host - 请求主机名，例如 cbm01.cn-huabei-1.xf-yun.com
	 * @param {Date} [customDate] - 可选，传入自定义时间用于签名（如测试时）
	 * @returns {{authorization: string, date: string}}
	 */
	buildAuthorization({
		apiKey,
		apiSecret,
		host,
		method = "POST",
		path
	}) {
		function getRFC1123Date() {
			const now = new Date()
			return now.toUTCString()
		}

		function buildSignatureOrigin(host, date, method, path) {
			return `host: ${host}\ndate: ${date}\n${method} ${path} HTTP/1.1`
		}

		function signHmacSha256Base64(text, secret) {
			const hash = CryptoJS.HmacSHA256(text, secret)
			return CryptoJS.enc.Base64.stringify(hash)
		}

		const date = getRFC1123Date()
		const signatureOrigin = buildSignatureOrigin(host, date, method, path)
		const signatureBase64 = signHmacSha256Base64(signatureOrigin, apiSecret)

		const authorizationOrigin =
			`api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signatureBase64}"`
		const authorization = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(authorizationOrigin))

		return {
			authorization,
			date
		}
	}


	/**
	 * 构建WebSocket连接URL
	 */
	buildWebSocketUrl() {
		// 解析URL
		const host = 'tts-api.xfyun.cn'
		const path = '/v2/tts'

		// 生成时间戳
		const date = new Date().toUTCString()

		// 构建签名字符串
		const signatureOrigin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`

		// 使用简化的HMAC-SHA256加密
		const signature = hmacSha256(signatureOrigin, XUNFEI_CONFIG.API_SECRET)
		const signatureBase64 = base64Encode(signature)

		// 构建authorization
		const authorization =
			`api_key="${XUNFEI_CONFIG.API_KEY}", algorithm="hmac-sha256", headers="host date request-line", signature="${signatureBase64}"`
		const authorizationBase64 = base64Encode(authorization)

		// 构建最终URL
		const params =
			`authorization=${encodeURIComponent(authorizationBase64)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`

		return `${XUNFEI_CONFIG.HOST_URL}?${params}`
	}

	/**
	 * 发送TTS请求
	 */
	sendTTSRequest(text) {
		const requestData = {
			common: {
				app_id: XUNFEI_CONFIG.APPID
			},
			business: {
				aue: "lame", // 音频编码格式
				auf: "audio/L16;rate=16000", // 音频采样率
				vcn: "x4_aisxping", // 发音人 - 小萍温柔女声
				speed: 55, // 语速 - 稍微加快
				volume: 60, // 音量 - 增大音量
				pitch: 50, // 音调
				bgs: 0, // 背景音 - 关闭背景音
				tte: "UTF8" // 文本编码格式
			},
			data: {
				status: 2, // 数据状态，2表示数据传输结束
				text: base64Encode(text)
			}
		}

		this.socketTask.send({
			data: JSON.stringify(requestData)
		})
	}

	/**
	 * 处理WebSocket消息
	 */
	handleMessage(data) {
		try {
			const response = JSON.parse(data)

			if (response.code !== 0) {
				console.error('科大讯飞TTS错误:', response.message)
				if (this.currentCallback) {
					this.currentCallback.reject(new Error(response.message))
				}
				return
			}

			// 收集音频数据
			if (response.data && response.data.audio) {
				this.audioData.push(response.data.audio)
			}

			// 检查是否传输完成
			if (response.data && response.data.status === 2) {
				// 合并所有音频数据
				const completeAudio = this.audioData.join('')

				if (this.currentCallback) {
					this.currentCallback.resolve(completeAudio)
				}

				// 关闭连接
				this.close()
			}

		} catch (error) {
			console.error('解析科大讯飞TTS响应失败:', error)
			if (this.currentCallback) {
				this.currentCallback.reject(error)
			}
		}
	}

	/**
	 * 关闭连接
	 */
	close() {
		if (this.socketTask) {
			this.socketTask.close()
			this.socketTask = null
		}
		this.isConnected = false
		this.audioData = []
		this.currentCallback = null
	}
}

// 创建单例实例
const xunfeiTTS = new XunfeiTTS()

/**
 * 导出语音合成方法
 * @param {string} text 要转换的文本
 * @returns {Promise<string>} 返回音频Base64数据
 */
export async function synthesizeText(text) {
	try {
		if (!text || text.trim().length === 0) {
			throw new Error('文本内容不能为空')
		}

		// 限制文本长度
		if (text.length > 500) {
			text = text.substring(0, 500)
		}

		console.log('开始语音合成:', text)

		// 尝试使用科大讯飞TTS，如果失败则使用模拟数据
		try {
			const audioBase64 = await xunfeiTTS.textToSpeech(text)
			console.log('科大讯飞语音合成完成')
			return audioBase64
		} catch (error) {
			console.warn('科大讯飞TTS失败，使用模拟音频:', error)
			const audioBase64 = await generateMockAudio(text)
			console.log('模拟语音合成完成')
			return audioBase64
		}

	} catch (error) {
		console.error('语音合成失败:', error)
		throw error
	}
}

/**
 * 生成模拟音频数据（用于测试）
 */
async function generateMockAudio(text) {
	// 模拟网络延迟
	await new Promise(resolve => setTimeout(resolve, 1000))

	// 返回一个很短的MP3文件的Base64编码（静音）
	// 这是一个1秒的静音MP3文件
	const mockMp3Base64 =
		"SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAAEAAABIADAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV6urq6urq6urq6urq6urq6urq6urq6urq6v////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAASDs90hvAAAAAAAAAAAAAAAAAAAA//tQxAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAAEAAABIADAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV6urq6urq6urq6urq6urq6urq6urq6urq6v////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAASDs90hvAAAAAAAAAAAAAAAAAAAA"

	console.log(`为文本生成模拟语音: ${text}`)

	return mockMp3Base64
}

export default xunfeiTTS
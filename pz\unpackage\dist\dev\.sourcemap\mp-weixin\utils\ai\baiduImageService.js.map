{"version": 3, "file": "baiduImageService.js", "sources": ["utils/ai/baiduImageService.js"], "sourcesContent": ["\n// 百度AI配置\nexport const BAIDU_CONFIG = {\n  apiKey: '956Ds55wM8lZyOqHc9Xl6Sdj',\n  secretKey: '6w7lYp7c9pQuDgOqGu0x1Dx8dDAo1HqH',\n  tokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',\n  // 菜品识别API\n  dishDetectUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v2/dish',\n  // 果蔬识别API\n  ingredientUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/classify/ingredient',\n  // 🔧 替代方案：使用通用物体和场景识别API\n  advancedGeneralUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v2/advanced_general',\n  objectDetectUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/object_detect',\n  // 图像内容理解API（可能已废弃，保留作为备用）\n  imageUnderstandingRequestUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/request',\n  imageUnderstandingResultUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/get-result'\n}\n\n// Access Token缓存\nlet cachedAccessToken = null\nlet tokenExpireTime = 0\n\n// API调用控制\nconst MAX_RETRIES = 2\nconst RETRY_DELAY_BASE = 8000\n\n/**\n * 将图片转为 base64\n */\nfunction getBase64(filePath) {\n  return new Promise((resolve, reject) => {\n    uni.getFileSystemManager().readFile({\n      filePath,\n      encoding: 'base64',\n      success: res => resolve(res.data),\n      fail: reject\n    })\n  })\n}\n\n/**\n * 延迟函数\n */\nfunction delay(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n/**\n * 获取百度API Access Token\n */\nexport async function getBaiduAccessToken() {\n  const currentTime = Date.now()\n  if (cachedAccessToken && currentTime < tokenExpireTime) {\n    return cachedAccessToken\n  }\n\n  try {\n    const response = await uni.request({\n      url: BAIDU_CONFIG.tokenUrl,\n      method: 'POST',\n      data: {\n        grant_type: 'client_credentials',\n        client_id: BAIDU_CONFIG.apiKey,\n        client_secret: BAIDU_CONFIG.secretKey\n      },\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    })\n\n    if (response.statusCode === 200 && response.data.access_token) {\n      cachedAccessToken = response.data.access_token\n      tokenExpireTime = currentTime + (response.data.expires_in - 300) * 1000\n      console.log('✅ 百度Access Token获取成功')\n      return cachedAccessToken\n    } else {\n      throw new Error('获取Access Token失败: ' + JSON.stringify(response.data))\n    }\n  } catch (error) {\n    console.error('❌ 获取百度Access Token失败:', error)\n    throw new Error('百度API认证失败')\n  }\n}\n\n/**\n * 百度菜品识别API调用\n */\nexport async function callDishDetectionAPI(filePath, retryCount = 0) {\n  try {\n    console.log('🍽️ 调用百度菜品识别API...')\n    const accessToken = await getBaiduAccessToken()\n    const base64 = await getBase64(filePath)\n\n    // 🔧 优化API调用参数\n    const requestData = {\n      image: base64,\n      top_num: 10, // 增加返回结果数量到10个\n      filter_threshold: 0.1, // 大幅降低过滤阈值，提高识别敏感度\n      baike_num: 5 // 增加百科信息数量\n    }\n\n    console.log('🔍 [DEBUG] 菜品识别API请求参数:')\n    console.log('  top_num:', requestData.top_num)\n    console.log('  filter_threshold:', requestData.filter_threshold)\n    console.log('  baike_num:', requestData.baike_num)\n    console.log('  图片base64长度:', base64.length)\n\n    const response = await uni.request({\n      url: `${BAIDU_CONFIG.dishDetectUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 20000 // 增加超时时间\n    })\n\n    if (response.statusCode === 200 && response.data) {\n      const result = response.data\n\n      // 🔍 调试日志：打印菜品识别API的完整原始响应\n      console.log('🔍 [DEBUG] 菜品识别API原始响应:')\n      console.log('=====================================')\n      console.log('HTTP状态码:', response.statusCode)\n      console.log('完整响应数据:', JSON.stringify(result, null, 2))\n      console.log('=====================================')\n\n      if (result.error_code) {\n        console.log('❌ [DEBUG] 菜品识别API返回错误:', result.error_code, result.error_msg)\n        if (result.error_code === 18 && retryCount < MAX_RETRIES) {\n          const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)\n          console.log(`⏳ 菜品识别QPS限制，等待${retryDelay / 1000}秒后重试...`)\n          await delay(retryDelay)\n          return await callDishDetectionAPI(filePath, retryCount + 1)\n        }\n        throw new Error(`菜品识别API错误: ${result.error_msg} (错误码: ${result.error_code})`)\n      }\n\n      if (result.result && result.result.length > 0) {\n        console.log('✅ 菜品识别成功，识别到', result.result.length, '个结果')\n\n        // 🔍 调试日志：详细打印每个识别结果\n        console.log('🔍 [DEBUG] 菜品识别详细结果:')\n        result.result.forEach((item, index) => {\n          console.log(`  ${index + 1}. 名称: ${item.name}`)\n          console.log(`     置信度: ${item.probability}`)\n          console.log(`     热量: ${item.calorie || '未知'}`)\n          console.log(`     百科信息: ${item.baike_info ? '有' : '无'}`)\n          console.log('     原始数据:', JSON.stringify(item, null, 2))\n        })\n\n        const processedData = result.result.map(item => ({\n          name: item.name,\n          confidence: item.probability,\n          calorie: item.calorie || null,\n          baike_info: item.baike_info || null\n        }))\n\n        console.log('🔍 [DEBUG] 菜品识别处理后数据:', JSON.stringify(processedData, null, 2))\n\n        return {\n          success: true,\n          data: processedData\n        }\n      } else {\n        console.log('⚠️ [DEBUG] 菜品识别未找到结果，result字段内容:', result.result)\n        return {\n          success: false,\n          data: [],\n          message: '未识别到菜品'\n        }\n      }\n    } else {\n      console.log('❌ [DEBUG] 菜品识别API调用失败，HTTP状态码:', response.statusCode)\n      console.log('❌ [DEBUG] 响应数据:', response.data)\n      throw new Error(`菜品识别API调用失败: HTTP ${response.statusCode}`)\n    }\n  } catch (error) {\n    console.error('❌ 菜品识别API失败:', error)\n    return {\n      success: false,\n      data: [],\n      error: error.message\n    }\n  }\n}\n\n/**\n * 百度果蔬识别API调用\n */\nexport async function callIngredientDetectionAPI(filePath, retryCount = 0) {\n  try {\n    console.log('🥬 调用百度果蔬识别API...')\n    const accessToken = await getBaiduAccessToken()\n    const base64 = await getBase64(filePath)\n\n    // 🔧 优化果蔬识别API参数\n    const requestData = {\n      image: base64,\n      top_num: 8 // 增加返回结果数量\n    }\n\n    console.log('🔍 [DEBUG] 果蔬识别API请求参数:')\n    console.log('  top_num:', requestData.top_num)\n    console.log('  图片base64长度:', base64.length)\n\n    const response = await uni.request({\n      url: `${BAIDU_CONFIG.ingredientUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 20000 // 增加超时时间\n    })\n\n    if (response.statusCode === 200 && response.data) {\n      const result = response.data\n\n      // 🔍 调试日志：打印果蔬识别API的完整原始响应\n      console.log('🔍 [DEBUG] 果蔬识别API原始响应:')\n      console.log('=====================================')\n      console.log('HTTP状态码:', response.statusCode)\n      console.log('完整响应数据:', JSON.stringify(result, null, 2))\n      console.log('=====================================')\n\n      if (result.error_code) {\n        console.log('❌ [DEBUG] 果蔬识别API返回错误:', result.error_code, result.error_msg)\n        if (result.error_code === 18 && retryCount < MAX_RETRIES) {\n          const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)\n          console.log(`⏳ 果蔬识别QPS限制，等待${retryDelay / 1000}秒后重试...`)\n          await delay(retryDelay)\n          return await callIngredientDetectionAPI(filePath, retryCount + 1)\n        }\n        throw new Error(`果蔬识别API错误: ${result.error_msg} (错误码: ${result.error_code})`)\n      }\n\n      if (result.result && result.result.length > 0) {\n        console.log('✅ 果蔬识别成功，识别到', result.result.length, '个结果')\n\n        // 🔍 调试日志：详细打印每个识别结果\n        console.log('🔍 [DEBUG] 果蔬识别详细结果:')\n        result.result.forEach((item, index) => {\n          console.log(`  ${index + 1}. 名称: ${item.name}`)\n          console.log(`     置信度: ${item.score}`)\n          console.log('     原始数据:', JSON.stringify(item, null, 2))\n        })\n\n        const processedData = result.result.map(item => ({\n          name: item.name,\n          confidence: item.score,\n          category: '果蔬类'\n        }))\n\n        console.log('🔍 [DEBUG] 果蔬识别处理后数据:', JSON.stringify(processedData, null, 2))\n\n        return {\n          success: true,\n          data: processedData\n        }\n      } else {\n        console.log('⚠️ [DEBUG] 果蔬识别未找到结果，result字段内容:', result.result)\n        return {\n          success: false,\n          data: [],\n          message: '未识别到果蔬'\n        }\n      }\n    } else {\n      console.log('❌ [DEBUG] 果蔬识别API调用失败，HTTP状态码:', response.statusCode)\n      console.log('❌ [DEBUG] 响应数据:', response.data)\n      throw new Error(`果蔬识别API调用失败: HTTP ${response.statusCode}`)\n    }\n  } catch (error) {\n    console.error('❌ 果蔬识别API失败:', error)\n    return {\n      success: false,\n      data: [],\n      error: error.message\n    }\n  }\n}\n\n// 🔧 移除旧的参数组合，现在使用简单的问题数组\n\n/**\n * 🔧 验证API端点和Access Token\n */\nasync function validateAPIEndpoint(accessToken) {\n  try {\n    console.log('🔍 [DEBUG] 验证图像理解API端点...')\n    console.log('  端点URL:', BAIDU_CONFIG.imageUnderstandingRequestUrl)\n    console.log('  Access Token前10位:', accessToken.substring(0, 10) + '...')\n\n    // 简单的端点可达性测试\n    const testUrl = new URL(BAIDU_CONFIG.imageUnderstandingRequestUrl)\n    console.log('  协议:', testUrl.protocol)\n    console.log('  主机:', testUrl.host)\n    console.log('  路径:', testUrl.pathname)\n\n    return true\n  } catch (error) {\n    console.error('❌ [DEBUG] API端点验证失败:', error)\n    return false\n  }\n}\n\n/**\n * 百度图像内容理解API调用（优化版）\n */\nexport async function callImageUnderstandingAPI(filePath, retryCount = 0, paramIndex = 0) {\n  try {\n    console.log('🔍 调用百度图像内容理解API...')\n    const accessToken = await getBaiduAccessToken()\n    const base64 = await getBase64(filePath)\n\n    // 🔧 验证API端点\n    const isEndpointValid = await validateAPIEndpoint(accessToken)\n    if (!isEndpointValid) {\n      throw new Error('API端点验证失败')\n    }\n\n    // 🔧 根据官方文档修复API参数格式\n    const questions = [\n      '这张图片中有什么食物？',\n      '图片中包含哪些菜品？',\n      '请识别图片中的食物',\n      '图片中的食物是什么？',\n      '描述图片中的食物内容'\n    ]\n\n    const currentQuestion = questions[paramIndex] || questions[0]\n\n    // 🔧 使用正确的API参数格式（根据官方文档）\n    const requestData = {\n      image: base64,\n      question: currentQuestion\n    }\n\n    console.log('🔍 [API-CHECK] ===== 图像理解API请求详情 =====')\n    console.log('  请求URL:', BAIDU_CONFIG.imageUnderstandingRequestUrl)\n    console.log('  Access Token长度:', accessToken.length)\n    console.log('  Access Token前10位:', accessToken.substring(0, 10) + '...')\n    console.log('  图片base64长度:', base64.length)\n    console.log('  图片base64前50位:', base64.substring(0, 50) + '...')\n    console.log('  请求参数组合索引:', paramIndex)\n    console.log('  重试次数:', retryCount)\n    console.log('  当前问题:', currentQuestion)\n    console.log('  完整请求数据:', JSON.stringify(requestData, null, 2))\n    console.log('  请求头:', JSON.stringify({\n      'Content-Type': 'application/json'\n    }, null, 2))\n    console.log('🔍 [API-CHECK] ===== 开始发送请求 =====')\n\n    // 🔧 第一步：提交图像理解请求（使用正确的JSON格式）\n    const requestResponse = await uni.request({\n      url: `${BAIDU_CONFIG.imageUnderstandingRequestUrl}?access_token=${accessToken}`,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json'\n      },\n      timeout: 20000 // 增加超时时间\n    })\n\n    // 🔍 详细分析API响应\n    console.log('🔍 [API-CHECK] ===== 图像理解API响应详情 =====')\n    console.log('  HTTP状态码:', requestResponse.statusCode)\n    console.log('  响应头:', JSON.stringify(requestResponse.header, null, 2))\n    console.log('  响应数据类型:', typeof requestResponse.data)\n    console.log('  响应数据是否为空:', !requestResponse.data)\n    console.log('  完整响应数据:', JSON.stringify(requestResponse.data, null, 2))\n\n    // 🔍 检查响应数据结构\n    if (requestResponse.data) {\n      console.log('🔍 [API-CHECK] 响应数据结构分析:')\n      console.log('  包含error_code:', 'error_code' in requestResponse.data)\n      console.log('  包含result:', 'result' in requestResponse.data)\n      console.log('  包含error_msg:', 'error_msg' in requestResponse.data)\n\n      if (requestResponse.data.error_code) {\n        console.log('  错误码值:', requestResponse.data.error_code)\n        console.log('  错误信息:', requestResponse.data.error_msg)\n      }\n\n      if (requestResponse.data.result && requestResponse.data.result.task_id) {\n        console.log('  任务ID值:', requestResponse.data.result.task_id)\n        console.log('  任务ID类型:', typeof requestResponse.data.result.task_id)\n        console.log('  任务ID长度:', requestResponse.data.result.task_id.length)\n      }\n    }\n    console.log('🔍 [API-CHECK] ===== 响应分析完成 =====')\n\n    if (requestResponse.statusCode !== 200 || !requestResponse.data) {\n      console.error('❌ [DEBUG] 图像理解API HTTP请求失败')\n      throw new Error(`图像理解请求失败: HTTP ${requestResponse.statusCode}`)\n    }\n\n    const requestResult = requestResponse.data\n\n    // 🔍 检查API返回的错误\n    if (requestResult.error_code) {\n      console.error('❌ [DEBUG] 图像理解API返回错误:')\n      console.error('  错误码:', requestResult.error_code)\n      console.error('  错误信息:', requestResult.error_msg)\n\n      if (requestResult.error_code === 18 && retryCount < MAX_RETRIES) {\n        const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)\n        console.log(`⏳ 图像理解QPS限制，等待${retryDelay / 1000}秒后重试...`)\n        await delay(retryDelay)\n        return await callImageUnderstandingAPI(filePath, retryCount + 1)\n      }\n      throw new Error(`图像理解请求错误: ${requestResult.error_msg} (错误码: ${requestResult.error_code})`)\n    }\n\n    // 🔍 检查任务ID（根据官方文档，应该在result.task_id中）\n    console.log('🔍 [API-CHECK] 检查任务ID结构:')\n    console.log('  requestResult.task_id:', requestResult.task_id)\n    console.log('  requestResult.result:', requestResult.result)\n    console.log('  requestResult.result?.task_id:', requestResult.result?.task_id)\n\n    let taskId = null\n    if (requestResult.result && requestResult.result.task_id) {\n      taskId = requestResult.result.task_id\n    } else if (requestResult.task_id) {\n      taskId = requestResult.task_id\n    }\n\n    if (!taskId) {\n      console.error('❌ [API-CHECK] 未获取到任务ID，完整响应:', JSON.stringify(requestResult, null, 2))\n\n      // 🔧 尝试使用不同的问题\n      if (paramIndex < questions.length - 1) {\n        console.log(`🔄 [API-CHECK] 尝试使用不同的问题，索引: ${paramIndex + 1}`)\n        await delay(1000) // 短暂延迟\n        return await callImageUnderstandingAPI(filePath, paramIndex + 1, retryCount)\n      }\n\n      // 🔧 如果所有问题都尝试过了，尝试重试\n      if (retryCount < MAX_RETRIES) {\n        console.log(`🔄 [API-CHECK] 重试图像理解API调用，第${retryCount + 1}次重试`)\n        await delay(RETRY_DELAY_BASE * (retryCount + 1))\n        return await callImageUnderstandingAPI(filePath, 0, retryCount + 1)\n      }\n\n      throw new Error('图像理解请求失败：未获取到任务ID，已尝试所有问题和重试')\n    }\n    console.log(`📋 图像理解任务ID: ${taskId}`)\n\n    // 第二步：轮询获取结果\n    let attempts = 0\n    const maxAttempts = 12 // 增加尝试次数\n    const pollInterval = 2500 // 缩短轮询间隔\n\n    while (attempts < maxAttempts) {\n      await delay(pollInterval)\n      attempts++\n\n      console.log(`🔄 [API-CHECK] 获取图像理解结果，第${attempts}次尝试...`)\n      const resultResponse = await uni.request({\n        url: `${BAIDU_CONFIG.imageUnderstandingResultUrl}?access_token=${accessToken}`,\n        method: 'POST',\n        data: {\n          task_id: taskId\n        },\n        header: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      })\n\n      console.log('🔍 [API-CHECK] 获取结果API响应:')\n      console.log('  HTTP状态码:', resultResponse.statusCode)\n      console.log('  完整响应:', JSON.stringify(resultResponse.data, null, 2))\n\n      if (resultResponse.statusCode === 200 && resultResponse.data) {\n        const result = resultResponse.data\n\n        if (result.error_code) {\n          throw new Error(`图像理解结果错误: ${result.error_msg} (错误码: ${result.error_code})`)\n        }\n\n        // 🔍 根据官方文档，检查result.ret_code状态\n        console.log('🔍 [API-CHECK] 检查任务状态:')\n        console.log('  result.result:', result.result)\n        console.log('  result.result?.ret_code:', result.result?.ret_code)\n        console.log('  result.result?.ret_msg:', result.result?.ret_msg)\n\n        if (result.result && result.result.ret_code === 0) {\n          console.log('✅ [API-CHECK] 图像内容理解完成')\n\n          // 🔍 调试日志：打印图像理解的完整结果\n          console.log('🔍 [API-CHECK] 图像理解API完整结果:')\n          console.log('=====================================')\n          console.log('任务状态:', result.result.ret_code)\n          console.log('状态信息:', result.result.ret_msg)\n          console.log('描述结果:', result.result.description)\n          console.log('完整结果数据:', JSON.stringify(result, null, 2))\n          console.log('=====================================')\n\n          // 🔍 处理图像理解结果（根据新的API格式）\n          const description = result.result.description || ''\n          console.log('🔍 [API-CHECK] 图像理解描述结果:', description)\n\n          // 🔧 将描述结果转换为标准格式\n          const mockData = [{\n            keyword: description ? [description] : [],\n            tag: [],\n            object: []\n          }]\n\n          console.log('🔍 [API-CHECK] 转换后的数据格式:', JSON.stringify(mockData, null, 2))\n\n          return {\n            success: true,\n            data: mockData\n          }\n        } else if (result.result && result.result.ret_code === 1) {\n          console.log(`⏳ [API-CHECK] 图像理解任务处理中，状态: ${result.result.ret_msg}，继续等待...`)\n        } else {\n          console.log('❌ [API-CHECK] 图像理解任务状态异常:', JSON.stringify(result, null, 2))\n          throw new Error('图像理解任务状态异常')\n        }\n        // task_status === 1 表示处理中，继续轮询\n      }\n    }\n\n    console.log('⚠️ 图像理解超时，但不影响整体流程')\n    return {\n      success: false,\n      data: [],\n      message: '图像理解超时'\n    }\n  } catch (error) {\n    console.error('❌ 图像内容理解API失败:', error)\n    return {\n      success: false,\n      data: [],\n      error: error.message\n    }\n  }\n}\n\n/**\n * 🔧 新增：食物相关性过滤函数\n */\n\n// 食物相关关键词库\nconst FOOD_KEYWORDS = [\n  // 主食类\n  '米饭', '面条', '面包', '馒头', '包子', '饺子', '馄饨', '粥', '粉', '面',\n  // 肉类\n  '猪肉', '牛肉', '羊肉', '鸡肉', '鸭肉', '鱼', '虾', '蟹', '肉', '鸡', '鸭', '鱼肉',\n  // 蔬菜类\n  '白菜', '菠菜', '萝卜', '土豆', '番茄', '黄瓜', '茄子', '豆角', '青菜', '蔬菜',\n  // 水果类\n  '苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '梨', '桃子', '水果',\n  // 饮料类\n  '水', '茶', '咖啡', '果汁', '牛奶', '酸奶', '饮料', '汤',\n  // 调料和其他\n  '盐', '糖', '醋', '酱油', '油', '蛋', '豆腐', '奶酪',\n  // 通用食物词汇\n  '食物', '菜', '饭', '餐', '食品', '小食', '点心', '甜品', '零食'\n]\n\n// 食物相关标签库\nconst FOOD_TAGS = [\n  'food', 'dish', 'meal', 'cuisine', 'cooking', 'restaurant', 'kitchen',\n  'vegetable', 'fruit', 'meat', 'seafood', 'drink', 'beverage', 'soup',\n  'rice', 'noodle', 'bread', 'cake', 'dessert', 'snack'\n]\n\n/**\n * 过滤食物相关关键词\n */\nfunction filterFoodRelatedKeywords(keywords) {\n  if (!Array.isArray(keywords)) return []\n\n  return keywords.filter(keyword => {\n    // 直接匹配食物关键词\n    if (FOOD_KEYWORDS.some(foodWord => keyword.includes(foodWord))) {\n      return true\n    }\n\n    // 检查是否包含食物相关的英文标签\n    if (FOOD_TAGS.some(tag => keyword.toLowerCase().includes(tag))) {\n      return true\n    }\n\n    // 检查常见食物描述词\n    const foodDescriptors = ['美食', '料理', '烹饪', '餐厅', '厨房', '营养', '健康']\n    if (foodDescriptors.some(desc => keyword.includes(desc))) {\n      return true\n    }\n\n    return false\n  })\n}\n\n/**\n * 过滤食物相关标签\n */\nfunction filterFoodRelatedTags(tags) {\n  if (!Array.isArray(tags)) return []\n\n  return tags.filter(tag => {\n    const tagLower = tag.toLowerCase()\n\n    // 匹配英文食物标签\n    if (FOOD_TAGS.some(foodTag => tagLower.includes(foodTag))) {\n      return true\n    }\n\n    // 匹配中文食物关键词\n    if (FOOD_KEYWORDS.some(foodWord => tag.includes(foodWord))) {\n      return true\n    }\n\n    return false\n  })\n}\n\n/**\n * 过滤食物相关物体\n */\nfunction filterFoodRelatedObjects(objects) {\n  if (!Array.isArray(objects)) return []\n\n  return objects.filter(obj => {\n    const objName = obj.name || obj.keyword || ''\n\n    // 检查物体名称是否包含食物关键词\n    if (FOOD_KEYWORDS.some(foodWord => objName.includes(foodWord))) {\n      return true\n    }\n\n    // 检查置信度，食物相关物体通常置信度较高\n    const confidence = obj.score || obj.probability || 0\n    if (confidence > 0.3 && FOOD_TAGS.some(tag => objName.toLowerCase().includes(tag))) {\n      return true\n    }\n\n    return false\n  }).map(obj => ({\n    name: obj.name || obj.keyword,\n    confidence: obj.score || obj.probability || 0,\n    category: '物体识别'\n  }))\n}\n\n/**\n * 🔧 新增：通用图像分析API（备用策略）\n */\nexport async function callGeneralImageAnalysisAPI(filePath) {\n  try {\n    console.log('🔍 调用百度通用图像分析API（备用策略）...')\n    const accessToken = await getBaiduAccessToken()\n    const base64 = await getBase64(filePath)\n\n    const response = await uni.request({\n      url: `https://aip.baidubce.com/rest/2.0/image-classify/v2/advanced_general?access_token=${accessToken}`,\n      method: 'POST',\n      data: {\n        image: base64,\n        baike_num: 5\n      },\n      header: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      timeout: 15000\n    })\n\n    if (response.statusCode === 200 && response.data && response.data.result) {\n      console.log('✅ 通用图像分析成功')\n      console.log('🔍 [DEBUG] 通用图像分析结果:', JSON.stringify(response.data.result, null, 2))\n\n      // 过滤出可能与食物相关的结果\n      const foodRelatedKeywords = ['食物', '菜', '饭', '面', '汤', '肉', '蔬菜', '水果', '饮料', '甜品', '小食']\n      const possibleFoodItems = response.data.result.filter(item => {\n        return foodRelatedKeywords.some(keyword => item.keyword.includes(keyword)) || item.score > 0.5\n      })\n\n      return {\n        success: true,\n        data: possibleFoodItems.map(item => ({\n          name: item.keyword,\n          confidence: item.score,\n          category: '通用识别'\n        }))\n      }\n    }\n\n    return { success: false, data: [] }\n  } catch (error) {\n    console.error('❌ 通用图像分析失败:', error)\n    return { success: false, data: [], error: error.message }\n  }\n}\n\n/**\n * 🔧 深度诊断：分析识别错误的专门函数\n */\nexport async function diagnoseFoodRecognitionError(filePath) {\n  console.log('🔍 [DIAGNOSTIC] 开始深度诊断食物识别错误...')\n  console.log('🔍 [DIAGNOSTIC] 图片路径:', filePath)\n\n  const diagnosticData = {\n    timestamp: new Date().toISOString(),\n    imagePath: filePath,\n    steps: [],\n    rawResponses: {},\n    processedResults: {},\n    errors: [],\n    analysis: {}\n  }\n\n  try {\n    // 步骤1：验证图片和Access Token\n    diagnosticData.steps.push('1. 验证图片和Access Token')\n    console.log('🔍 [DIAGNOSTIC] 步骤1: 验证图片和Access Token')\n\n    const accessToken = await getBaiduAccessToken()\n    const base64 = await getBase64(filePath)\n\n    diagnosticData.accessToken = {\n      length: accessToken.length,\n      prefix: accessToken.substring(0, 10) + '...',\n      valid: accessToken.length > 20\n    }\n\n    diagnosticData.imageData = {\n      base64Length: base64.length,\n      isValid: base64.length > 100,\n      prefix: base64.substring(0, 50) + '...'\n    }\n\n    console.log('✅ [DIAGNOSTIC] Access Token长度:', accessToken.length)\n    console.log('✅ [DIAGNOSTIC] 图片Base64长度:', base64.length)\n\n    // 步骤2：调用图像理解API并记录完整响应\n    diagnosticData.steps.push('2. 调用图像理解API')\n    console.log('🔍 [DIAGNOSTIC] 步骤2: 调用图像理解API')\n\n    const understandingResult = await callImageUnderstandingAPI(filePath)\n    diagnosticData.rawResponses.imageUnderstanding = understandingResult\n\n    console.log('🔍 [DIAGNOSTIC] 图像理解API原始响应:')\n    console.log(JSON.stringify(understandingResult, null, 2))\n\n    // 步骤3：调用备用API进行对比\n    diagnosticData.steps.push('3. 调用备用API进行对比')\n    console.log('🔍 [DIAGNOSTIC] 步骤3: 调用备用API进行对比')\n\n    const generalResult = await callGeneralImageAnalysisAPI(filePath)\n    diagnosticData.rawResponses.generalAnalysis = generalResult\n\n    console.log('🔍 [DIAGNOSTIC] 通用图像分析API原始响应:')\n    console.log(JSON.stringify(generalResult, null, 2))\n\n    // 步骤4：分析处理逻辑\n    diagnosticData.steps.push('4. 分析处理逻辑')\n    console.log('🔍 [DIAGNOSTIC] 步骤4: 分析处理逻辑')\n\n    if (understandingResult.success && understandingResult.data) {\n      const analysisResult = analyzeImageUnderstandingData(understandingResult.data)\n      diagnosticData.processedResults.imageUnderstanding = analysisResult\n\n      console.log('🔍 [DIAGNOSTIC] 图像理解数据分析结果:')\n      console.log(JSON.stringify(analysisResult, null, 2))\n    }\n\n    // 步骤5：生成诊断分析\n    diagnosticData.analysis = generateDiagnosticAnalysis(diagnosticData)\n\n    console.log('🔍 [DIAGNOSTIC] 诊断分析完成')\n    console.log('='.repeat(80))\n    console.log('📊 诊断报告:')\n    console.log(JSON.stringify(diagnosticData.analysis, null, 2))\n    console.log('='.repeat(80))\n\n    return diagnosticData\n\n  } catch (error) {\n    console.error('❌ [DIAGNOSTIC] 诊断过程异常:', error)\n    diagnosticData.errors.push({\n      step: '诊断过程',\n      error: error.message,\n      stack: error.stack\n    })\n    return diagnosticData\n  }\n}\n\n/**\n * 🔧 新的食物识别方案：通义千问VL-MAX + DeepSeek\n * 替换有问题的百度AI图像理解API\n */\nexport async function comprehensiveFoodRecognition(filePath) {\n  console.log('🚀 [NEW-SOLUTION] 开始新的食物识别方案...')\n  console.log('🔍 [NEW-SOLUTION] 输入图片路径:', filePath)\n  console.log('🔍 [NEW-SOLUTION] 策略: 通义千问VL-MAX视觉理解 + DeepSeek结构化处理')\n\n  try {\n    // 🔧 第一步：使用通义千问VL-MAX进行图像视觉理解\n    console.log('🔍 [NEW-SOLUTION] 第一步：调用通义千问VL-MAX进行图像视觉理解...')\n    const visionResult = await callQwenVLMaxAPI(filePath)\n\n    if (!visionResult.success) {\n      console.error('❌ [NEW-SOLUTION] 通义千问VL-MAX调用失败:', visionResult.error)\n      return {\n        success: false,\n        error: visionResult.error,\n        data: {\n          summary: `通义千问VL-MAX调用失败: ${visionResult.error}`,\n          confidence: 0,\n          recognizedFoods: []\n        }\n      }\n    }\n\n    console.log('✅ [NEW-SOLUTION] 通义千问VL-MAX识别成功')\n    console.log('🔍 [NEW-SOLUTION] 视觉理解结果:', visionResult.description)\n\n    // 🔧 第二步：使用DeepSeek进行结构化处理\n    console.log('🔍 [NEW-SOLUTION] 第二步：调用DeepSeek进行结构化处理...')\n    const structuredResult = await callDeepSeekForStructuring(visionResult.description)\n\n    if (!structuredResult.success) {\n      console.error('❌ [NEW-SOLUTION] DeepSeek结构化处理失败:', structuredResult.error)\n      return {\n        success: false,\n        error: structuredResult.error,\n        data: {\n          summary: `DeepSeek结构化处理失败: ${structuredResult.error}`,\n          confidence: 0,\n          recognizedFoods: []\n        }\n      }\n    }\n\n    console.log('✅ [NEW-SOLUTION] DeepSeek结构化处理成功')\n    console.log('🔍 [NEW-SOLUTION] 结构化结果:', JSON.stringify(structuredResult.data, null, 2))\n\n    // 🔧 第三步：格式化返回结果，保持与现有UI的兼容性\n    console.log('🔍 [NEW-SOLUTION] 第三步：格式化返回结果...')\n\n    const results = {\n      recognizedFoods: structuredResult.data.recognizedFoods || [],\n      summary: structuredResult.data.summary || '通过新的AI视觉识别方案成功识别食物',\n      confidence: structuredResult.data.confidence || 85,\n      rawVisionResult: visionResult.description,\n      rawStructuredResult: structuredResult.data\n    }\n\n    console.log('✅ [NEW-SOLUTION] 新方案识别完成:')\n    console.log(`  识别食物数量: ${results.recognizedFoods.length} 个`)\n    console.log(`  整体置信度: ${results.confidence}%`)\n    console.log(`  识别摘要: ${results.summary}`)\n    console.log('🔍 [NEW-SOLUTION] 识别的食物详情:')\n    results.recognizedFoods.forEach((food, index) => {\n      console.log(`  ${index + 1}. ${food.name} (置信度: ${food.confidence}) - ${food.source}`)\n    })\n\n    return {\n      success: true,\n      data: results\n    }\n\n  } catch (error) {\n    console.error('❌ [NEW-SOLUTION] 新方案识别异常:', error)\n    return {\n      success: false,\n      error: error.message,\n      data: {\n        summary: `新方案识别异常: ${error.message}`,\n        confidence: 0,\n        recognizedFoods: []\n      }\n    }\n  }\n}\n\n/**\n * 🔧 通义千问VL-MAX API调用函数\n */\nasync function callQwenVLMaxAPI(filePath) {\n  console.log('🔍 [QWEN-VL] 开始调用通义千问VL-MAX API...')\n\n  try {\n    // 获取图片的base64编码\n    const base64 = await getBase64(filePath)\n    console.log('🔍 [QWEN-VL] 图片base64编码完成，长度:', base64.length)\n\n    // 通义千问VL-MAX API配置\n    const QWEN_VL_CONFIG = {\n      baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',\n      apiKey: 'sk-948691927af4402a92cb566e1d8e153f', // 从用户记忆中获取的API密钥\n      model: 'qwen-vl-max'\n    }\n\n    // 构建请求数据\n    const requestData = {\n      model: QWEN_VL_CONFIG.model,\n      messages: [\n        {\n          role: 'system',\n          content: '你是一个专业的食物识别专家。请详细描述图片中的食物内容，包括具体的食物名称、数量、外观特征和可能的营养成分信息。'\n        },\n        {\n          role: 'user',\n          content: [\n            {\n              type: 'image',\n              image_url: {\n                url: `data:image/jpeg;base64,${base64}`\n              }\n            },\n            {\n              type: 'text',\n              text: '请详细识别和描述这张图片中的食物，包括：1. 具体的食物名称；2. 食物的数量或份量；3. 食物的外观特征；4. 可能的营养成分信息。请用中文回答。'\n            }\n          ]\n        }\n      ],\n      temperature: 0.1,\n      max_tokens: 1000\n    }\n\n    console.log('🔍 [QWEN-VL] 发送API请求...')\n    console.log('🔍 [QWEN-VL] 请求URL:', QWEN_VL_CONFIG.baseUrl)\n    console.log('🔍 [QWEN-VL] 请求模型:', QWEN_VL_CONFIG.model)\n\n    // 发送API请求\n    const response = await uni.request({\n      url: QWEN_VL_CONFIG.baseUrl,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${QWEN_VL_CONFIG.apiKey}`,\n        'Accept': 'application/json'\n      },\n      timeout: 30000\n    })\n\n    console.log('🔍 [QWEN-VL] API响应状态码:', response.statusCode)\n    console.log('🔍 [QWEN-VL] API完整响应:', JSON.stringify(response.data, null, 2))\n\n    if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices[0]) {\n      const description = response.data.choices[0].message.content\n      console.log('✅ [QWEN-VL] 通义千问VL-MAX识别成功')\n      console.log('🔍 [QWEN-VL] 识别描述:', description)\n\n      return {\n        success: true,\n        description: description,\n        rawResponse: response.data\n      }\n    } else {\n      throw new Error(`通义千问VL-MAX API响应异常: ${response.statusCode}`)\n    }\n\n  } catch (error) {\n    console.error('❌ [QWEN-VL] 通义千问VL-MAX API调用失败:', error)\n    return {\n      success: false,\n      error: error.message || '通义千问VL-MAX API调用失败'\n    }\n  }\n}\n\n/**\n * 🔧 DeepSeek结构化处理函数\n */\nasync function callDeepSeekForStructuring(description) {\n  console.log('🔍 [DEEPSEEK] 开始调用DeepSeek进行结构化处理...')\n  console.log('🔍 [DEEPSEEK] 输入描述:', description)\n\n  try {\n    // 从AI助手模块获取DeepSeek配置\n    const { DEEPSEEK_BASE_URL, DEEPSEEK_API_KEY } = await import('./config.js')\n\n    console.log('🔍 [DEEPSEEK] DeepSeek配置:')\n    console.log('  Base URL:', DEEPSEEK_BASE_URL)\n    console.log('  API Key长度:', DEEPSEEK_API_KEY ? DEEPSEEK_API_KEY.length : 0)\n\n    // 构建结构化处理的提示词\n    const structuringPrompt = `请将以下食物描述转换为标准化的JSON格式数据。\n\n输入描述：\n${description}\n\n请返回以下格式的JSON数据（不要包含任何markdown代码块标记）：\n{\n  \"recognizedFoods\": [\n    {\n      \"name\": \"食物名称\",\n      \"confidence\": 0.85,\n      \"source\": \"AI视觉识别\",\n      \"category\": \"食物类别\",\n      \"quantity\": \"数量描述\",\n      \"appearance\": \"外观特征\",\n      \"nutrition\": \"营养信息\"\n    }\n  ],\n  \"summary\": \"识别摘要\",\n  \"confidence\": 85\n}\n\n要求：\n1. 从描述中提取所有可识别的食物\n2. 为每个食物分配合理的置信度（0-100）\n3. 提供简洁的识别摘要\n4. 只返回JSON数据，不要其他文字`\n\n    console.log('🔍 [DEEPSEEK] 发送结构化处理请求...')\n    console.log('🔍 [DEEPSEEK] 提示词长度:', structuringPrompt.length)\n\n    // 构建DeepSeek请求数据\n    const requestData = {\n      model: 'deepseek-chat',\n      messages: [\n        {\n          role: 'system',\n          content: '你是一个专业的数据结构化处理专家，擅长将自然语言描述转换为标准化的JSON格式数据。'\n        },\n        {\n          role: 'user',\n          content: structuringPrompt\n        }\n      ],\n      temperature: 0.1,\n      max_tokens: 1500,\n      stream: false\n    }\n\n    // 发送DeepSeek API请求\n    const response = await uni.request({\n      url: DEEPSEEK_BASE_URL,\n      method: 'POST',\n      data: requestData,\n      header: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,\n        'Accept': 'application/json'\n      },\n      timeout: 30000\n    })\n\n    console.log('🔍 [DEEPSEEK] API响应状态码:', response.statusCode)\n    console.log('🔍 [DEEPSEEK] API完整响应:', JSON.stringify(response.data, null, 2))\n\n    if (response.statusCode === 200 && response.data && response.data.choices && response.data.choices[0]) {\n      const structuredContent = response.data.choices[0].message.content\n      console.log('✅ [DEEPSEEK] DeepSeek结构化处理成功')\n      console.log('🔍 [DEEPSEEK] 结构化内容:', structuredContent)\n\n      try {\n        // 解析JSON结果\n        const jsonData = JSON.parse(structuredContent)\n        console.log('✅ [DEEPSEEK] JSON解析成功')\n        console.log('🔍 [DEEPSEEK] 解析后的数据:', JSON.stringify(jsonData, null, 2))\n\n        return {\n          success: true,\n          data: jsonData,\n          rawResponse: response.data\n        }\n      } catch (parseError) {\n        console.error('❌ [DEEPSEEK] JSON解析失败:', parseError)\n        console.log('🔍 [DEEPSEEK] 原始内容:', structuredContent)\n\n        // 如果JSON解析失败，返回一个默认结构\n        return {\n          success: true,\n          data: {\n            recognizedFoods: [{\n              name: '识别的食物',\n              confidence: 0.7,\n              source: 'AI视觉识别',\n              category: '未分类',\n              quantity: '未知',\n              appearance: structuredContent.substring(0, 100),\n              nutrition: '需要进一步分析'\n            }],\n            summary: '通过AI视觉识别获得食物信息',\n            confidence: 70\n          },\n          rawResponse: response.data\n        }\n      }\n    } else {\n      throw new Error(`DeepSeek API响应异常: ${response.statusCode}`)\n    }\n\n  } catch (error) {\n    console.error('❌ [DEEPSEEK] DeepSeek结构化处理失败:', error)\n    return {\n      success: false,\n      error: error.message || 'DeepSeek结构化处理失败'\n    }\n  }\n}\n\n// 🔧 保留原有的辅助函数以确保兼容性（已简化，新方案不再使用）\n\n/**\n * 🔧 分析图像理解数据的详细函数\n */\nfunction analyzeImageUnderstandingData(data) {\n  const analysis = {\n    totalItems: data.length,\n    keywordAnalysis: {},\n    tagAnalysis: {},\n    objectAnalysis: {},\n    foodRelatedItems: [],\n    nonFoodItems: [],\n    confidenceDistribution: {},\n    potentialIssues: []\n  }\n\n  data.forEach((item, index) => {\n    console.log(`🔍 [ANALYSIS] 分析项目 ${index + 1}:`, JSON.stringify(item, null, 2))\n\n    // 分析关键词\n    if (item.keyword && Array.isArray(item.keyword)) {\n      analysis.keywordAnalysis[`item_${index}`] = {\n        total: item.keyword.length,\n        keywords: item.keyword,\n        foodRelated: filterFoodRelatedKeywords(item.keyword),\n        nonFoodRelated: item.keyword.filter(kw => !filterFoodRelatedKeywords([kw]).length)\n      }\n    }\n\n    // 分析标签\n    if (item.tag && Array.isArray(item.tag)) {\n      analysis.tagAnalysis[`item_${index}`] = {\n        total: item.tag.length,\n        tags: item.tag,\n        foodRelated: filterFoodRelatedTags(item.tag),\n        nonFoodRelated: item.tag.filter(tag => !filterFoodRelatedTags([tag]).length)\n      }\n    }\n\n    // 分析物体识别\n    if (item.object && Array.isArray(item.object)) {\n      analysis.objectAnalysis[`item_${index}`] = {\n        total: item.object.length,\n        objects: item.object,\n        foodRelated: filterFoodRelatedObjects(item.object),\n        nonFoodRelated: item.object.filter(obj => !filterFoodRelatedObjects([obj]).length)\n      }\n    }\n\n    // 判断整个项目是否与食物相关\n    const hasKeywords = item.keyword && filterFoodRelatedKeywords(item.keyword).length > 0\n    const hasTags = item.tag && filterFoodRelatedTags(item.tag).length > 0\n    const hasObjects = item.object && filterFoodRelatedObjects(item.object).length > 0\n\n    if (hasKeywords || hasTags || hasObjects) {\n      analysis.foodRelatedItems.push({\n        index,\n        item,\n        reasons: {\n          keywords: hasKeywords,\n          tags: hasTags,\n          objects: hasObjects\n        }\n      })\n    } else {\n      analysis.nonFoodItems.push({\n        index,\n        item,\n        reasons: '无食物相关内容'\n      })\n    }\n  })\n\n  // 检测潜在问题\n  if (analysis.foodRelatedItems.length === 0) {\n    analysis.potentialIssues.push('未检测到任何食物相关内容')\n  }\n\n  if (analysis.nonFoodItems.length > analysis.foodRelatedItems.length) {\n    analysis.potentialIssues.push('非食物内容多于食物内容')\n  }\n\n  // 检查是否有明显的错误识别\n  const allKeywords = Object.values(analysis.keywordAnalysis).flatMap(ka => ka.keywords)\n  const suspiciousKeywords = ['面包', 'bread', '全麦', 'wheat']\n  const hasSuspiciousKeywords = allKeywords.some(kw =>\n    suspiciousKeywords.some(suspicious => kw.includes(suspicious))\n  )\n\n  if (hasSuspiciousKeywords) {\n    analysis.potentialIssues.push('检测到可能的错误识别关键词（面包相关）')\n  }\n\n  return analysis\n}\n\n/**\n * 🔧 生成诊断分析报告\n */\nfunction generateDiagnosticAnalysis(diagnosticData) {\n  const analysis = {\n    summary: '',\n    issues: [],\n    recommendations: [],\n    apiStatus: {},\n    dataQuality: {},\n    processingLogic: {}\n  }\n\n  // 分析API状态\n  analysis.apiStatus = {\n    imageUnderstanding: {\n      called: !!diagnosticData.rawResponses.imageUnderstanding,\n      success: diagnosticData.rawResponses.imageUnderstanding?.success || false,\n      hasData: !!(diagnosticData.rawResponses.imageUnderstanding?.data?.length),\n      dataCount: diagnosticData.rawResponses.imageUnderstanding?.data?.length || 0\n    },\n    generalAnalysis: {\n      called: !!diagnosticData.rawResponses.generalAnalysis,\n      success: diagnosticData.rawResponses.generalAnalysis?.success || false,\n      hasData: !!(diagnosticData.rawResponses.generalAnalysis?.data?.length),\n      dataCount: diagnosticData.rawResponses.generalAnalysis?.data?.length || 0\n    }\n  }\n\n  // 分析数据质量\n  if (diagnosticData.processedResults.imageUnderstanding) {\n    const processed = diagnosticData.processedResults.imageUnderstanding\n    analysis.dataQuality = {\n      totalItems: processed.totalItems,\n      foodRelatedItems: processed.foodRelatedItems.length,\n      nonFoodItems: processed.nonFoodItems.length,\n      foodRatio: processed.totalItems > 0 ?\n        (processed.foodRelatedItems.length / processed.totalItems * 100).toFixed(1) + '%' : '0%',\n      potentialIssues: processed.potentialIssues\n    }\n  }\n\n  // 生成问题分析\n  if (!analysis.apiStatus.imageUnderstanding.success) {\n    analysis.issues.push('图像理解API调用失败')\n    analysis.recommendations.push('检查API密钥和网络连接')\n  }\n\n  if (analysis.apiStatus.imageUnderstanding.success && !analysis.apiStatus.imageUnderstanding.hasData) {\n    analysis.issues.push('图像理解API成功但无返回数据')\n    analysis.recommendations.push('检查图片格式和API参数配置')\n  }\n\n  if (analysis.dataQuality.foodRelatedItems === 0) {\n    analysis.issues.push('未识别到任何食物相关内容')\n    analysis.recommendations.push('检查食物关键词库和过滤逻辑')\n  }\n\n  if (analysis.dataQuality.potentialIssues?.includes('检测到可能的错误识别关键词（面包相关）')) {\n    analysis.issues.push('检测到错误识别：家常菜被识别为面包')\n    analysis.recommendations.push('优化API参数，特别是question和scenes参数')\n    analysis.recommendations.push('检查图片质量和拍摄角度')\n  }\n\n  // 生成总结\n  const issueCount = analysis.issues.length\n  const recommendationCount = analysis.recommendations.length\n\n  analysis.summary = `诊断完成：发现 ${issueCount} 个问题，提供 ${recommendationCount} 条建议。`\n\n  if (issueCount === 0) {\n    analysis.summary += ' API工作正常，可能是图片内容或质量问题。'\n  } else {\n    analysis.summary += ` 主要问题：${analysis.issues[0]}`\n  }\n\n  return analysis\n}\n\n/**\n * 🔧 保留原有摘要生成函数（向后兼容）\n */\nfunction generateRecognitionSummary(results) {\n  return generateOptimizedRecognitionSummary(results)\n}\n"], "names": ["uni"], "mappings": ";;;AAEY,MAAC,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,UAAU;AAAA;AAAA,EAEV,eAAe;AAAA;AAAA,EAEf,eAAe;AAAA;AAAA,EAEf,oBAAoB;AAAA,EACpB,iBAAiB;AAAA;AAAA,EAEjB,8BAA8B;AAAA,EAC9B,6BAA6B;AAC/B;AAGA,IAAI,oBAAoB;AACxB,IAAI,kBAAkB;AAGtB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AAKzB,SAAS,UAAU,UAAU;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,wBAAI,qBAAsB,EAAC,SAAS;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,SAAS,SAAO,QAAQ,IAAI,IAAI;AAAA,MAChC,MAAM;AAAA,IACZ,CAAK;AAAA,EACL,CAAG;AACH;AAKA,SAAS,MAAM,IAAI;AACjB,SAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AACvD;AAKO,eAAe,sBAAsB;AAC1C,QAAM,cAAc,KAAK,IAAK;AAC9B,MAAI,qBAAqB,cAAc,iBAAiB;AACtD,WAAO;AAAA,EACR;AAED,MAAI;AACF,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK,aAAa;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW,aAAa;AAAA,QACxB,eAAe,aAAa;AAAA,MAC7B;AAAA,MACD,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,IACP,CAAK;AAED,QAAI,SAAS,eAAe,OAAO,SAAS,KAAK,cAAc;AAC7D,0BAAoB,SAAS,KAAK;AAClC,wBAAkB,eAAe,SAAS,KAAK,aAAa,OAAO;AACnEA,oBAAAA,MAAA,MAAA,OAAA,uCAAY,sBAAsB;AAClC,aAAO;AAAA,IACb,OAAW;AACL,YAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,SAAS,IAAI,CAAC;AAAA,IACrE;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,uCAAc,yBAAyB,KAAK;AAC5C,UAAM,IAAI,MAAM,WAAW;AAAA,EAC5B;AACH;AA8MA,eAAe,oBAAoB,aAAa;AAC9C,MAAI;AACFA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,2BAA2B;AACvCA,kBAAY,MAAA,MAAA,OAAA,wCAAA,YAAY,aAAa,4BAA4B;AACjEA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,uBAAuB,YAAY,UAAU,GAAG,EAAE,IAAI,KAAK;AAGvE,UAAM,UAAU,IAAI,IAAI,aAAa,4BAA4B;AACjEA,kBAAA,MAAA,MAAA,OAAA,wCAAY,SAAS,QAAQ,QAAQ;AACrCA,kBAAY,MAAA,MAAA,OAAA,wCAAA,SAAS,QAAQ,IAAI;AACjCA,kBAAA,MAAA,MAAA,OAAA,wCAAY,SAAS,QAAQ,QAAQ;AAErC,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wCAAc,wBAAwB,KAAK;AAC3C,WAAO;AAAA,EACR;AACH;AAKO,eAAe,0BAA0B,UAAU,aAAa,GAAG,aAAa,GAAG;;AACxF,MAAI;AACFA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,qBAAqB;AACjC,UAAM,cAAc,MAAM,oBAAqB;AAC/C,UAAM,SAAS,MAAM,UAAU,QAAQ;AAGvC,UAAM,kBAAkB,MAAM,oBAAoB,WAAW;AAC7D,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,WAAW;AAAA,IAC5B;AAGD,UAAM,YAAY;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,UAAM,kBAAkB,UAAU,UAAU,KAAK,UAAU,CAAC;AAG5D,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,UAAU;AAAA,IACX;AAEDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,wCAAwC;AACpDA,kBAAY,MAAA,MAAA,OAAA,wCAAA,YAAY,aAAa,4BAA4B;AACjEA,6EAAY,qBAAqB,YAAY,MAAM;AACnDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,uBAAuB,YAAY,UAAU,GAAG,EAAE,IAAI,KAAK;AACvEA,kBAAA,MAAA,MAAA,OAAA,wCAAY,iBAAiB,OAAO,MAAM;AAC1CA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,mBAAmB,OAAO,UAAU,GAAG,EAAE,IAAI,KAAK;AAC9DA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,eAAe,UAAU;AACrCA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,WAAW,UAAU;AACjCA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,WAAW,eAAe;AACtCA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,aAAa,KAAK,UAAU,aAAa,MAAM,CAAC,CAAC;AAC7DA,kBAAA,MAAA,MAAA,OAAA,wCAAY,UAAU,KAAK,UAAU;AAAA,MACnC,gBAAgB;AAAA,IACtB,GAAO,MAAM,CAAC,CAAC;AACXA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,mCAAmC;AAG/C,UAAM,kBAAkB,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACxC,KAAK,GAAG,aAAa,4BAA4B,iBAAiB,WAAW;AAAA,MAC7E,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA;AAAA,IACf,CAAK;AAGDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,wCAAwC;AACpDA,kBAAA,MAAA,MAAA,OAAA,wCAAY,cAAc,gBAAgB,UAAU;AACpDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU,KAAK,UAAU,gBAAgB,QAAQ,MAAM,CAAC,CAAC;AACrEA,kBAAA,MAAA,MAAA,OAAA,wCAAY,aAAa,OAAO,gBAAgB,IAAI;AACpDA,6EAAY,eAAe,CAAC,gBAAgB,IAAI;AAChDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,aAAa,KAAK,UAAU,gBAAgB,MAAM,MAAM,CAAC,CAAC;AAGtE,QAAI,gBAAgB,MAAM;AACxBA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,0BAA0B;AACtCA,oBAAA,MAAA,MAAA,OAAA,wCAAY,mBAAmB,gBAAgB,gBAAgB,IAAI;AACnEA,+EAAY,eAAe,YAAY,gBAAgB,IAAI;AAC3DA,oBAAY,MAAA,MAAA,OAAA,wCAAA,kBAAkB,eAAe,gBAAgB,IAAI;AAEjE,UAAI,gBAAgB,KAAK,YAAY;AACnCA,sBAAY,MAAA,MAAA,OAAA,wCAAA,WAAW,gBAAgB,KAAK,UAAU;AACtDA,sBAAY,MAAA,MAAA,OAAA,wCAAA,WAAW,gBAAgB,KAAK,SAAS;AAAA,MACtD;AAED,UAAI,gBAAgB,KAAK,UAAU,gBAAgB,KAAK,OAAO,SAAS;AACtEA,4BAAA,MAAA,OAAA,wCAAY,YAAY,gBAAgB,KAAK,OAAO,OAAO;AAC3DA,4BAAA,MAAA,OAAA,wCAAY,aAAa,OAAO,gBAAgB,KAAK,OAAO,OAAO;AACnEA,4BAAA,MAAA,OAAA,wCAAY,aAAa,gBAAgB,KAAK,OAAO,QAAQ,MAAM;AAAA,MACpE;AAAA,IACF;AACDA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,mCAAmC;AAE/C,QAAI,gBAAgB,eAAe,OAAO,CAAC,gBAAgB,MAAM;AAC/DA,oBAAAA,MAAc,MAAA,SAAA,wCAAA,4BAA4B;AAC1C,YAAM,IAAI,MAAM,kBAAkB,gBAAgB,UAAU,EAAE;AAAA,IAC/D;AAED,UAAM,gBAAgB,gBAAgB;AAGtC,QAAI,cAAc,YAAY;AAC5BA,oBAAAA,MAAA,MAAA,SAAA,wCAAc,wBAAwB;AACtCA,oBAAA,MAAA,MAAA,SAAA,wCAAc,UAAU,cAAc,UAAU;AAChDA,oBAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,cAAc,SAAS;AAEhD,UAAI,cAAc,eAAe,MAAM,aAAa,aAAa;AAC/D,cAAM,aAAa,oBAAoB,aAAa;AACpDA,4BAAY,MAAA,OAAA,wCAAA,iBAAiB,aAAa,GAAI,SAAS;AACvD,cAAM,MAAM,UAAU;AACtB,eAAO,MAAM,0BAA0B,UAAU,aAAa,CAAC;AAAA,MAChE;AACD,YAAM,IAAI,MAAM,aAAa,cAAc,SAAS,UAAU,cAAc,UAAU,GAAG;AAAA,IAC1F;AAGDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,0BAA0B;AACtCA,kBAAY,MAAA,MAAA,OAAA,wCAAA,4BAA4B,cAAc,OAAO;AAC7DA,kBAAA,MAAA,MAAA,OAAA,wCAAY,2BAA2B,cAAc,MAAM;AAC3DA,kBAAA,MAAA,MAAA,OAAA,wCAAY,qCAAoC,mBAAc,WAAd,mBAAsB,OAAO;AAE7E,QAAI,SAAS;AACb,QAAI,cAAc,UAAU,cAAc,OAAO,SAAS;AACxD,eAAS,cAAc,OAAO;AAAA,IACpC,WAAe,cAAc,SAAS;AAChC,eAAS,cAAc;AAAA,IACxB;AAED,QAAI,CAAC,QAAQ;AACXA,oBAAAA,6DAAc,gCAAgC,KAAK,UAAU,eAAe,MAAM,CAAC,CAAC;AAGpF,UAAI,aAAa,UAAU,SAAS,GAAG;AACrCA,4BAAY,MAAA,OAAA,wCAAA,gCAAgC,aAAa,CAAC,EAAE;AAC5D,cAAM,MAAM,GAAI;AAChB,eAAO,MAAM,0BAA0B,UAAU,aAAa,GAAG,UAAU;AAAA,MAC5E;AAGD,UAAI,aAAa,aAAa;AAC5BA,4BAAY,MAAA,OAAA,wCAAA,+BAA+B,aAAa,CAAC,KAAK;AAC9D,cAAM,MAAM,oBAAoB,aAAa,EAAE;AAC/C,eAAO,MAAM,0BAA0B,UAAU,GAAG,aAAa,CAAC;AAAA,MACnE;AAED,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAC/C;AACDA,kBAAA,MAAA,MAAA,OAAA,wCAAY,gBAAgB,MAAM,EAAE;AAGpC,QAAI,WAAW;AACf,UAAM,cAAc;AACpB,UAAM,eAAe;AAErB,WAAO,WAAW,aAAa;AAC7B,YAAM,MAAM,YAAY;AACxB;AAEAA,oBAAY,MAAA,MAAA,OAAA,wCAAA,4BAA4B,QAAQ,QAAQ;AACxD,YAAM,iBAAiB,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACvC,KAAK,GAAG,aAAa,2BAA2B,iBAAiB,WAAW;AAAA,QAC5E,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,SAAS;AAAA,QACV;AAAA,QACD,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,SAAS;AAAA,MACjB,CAAO;AAEDA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,2BAA2B;AACvCA,oBAAY,MAAA,MAAA,OAAA,wCAAA,cAAc,eAAe,UAAU;AACnDA,oBAAAA,MAAA,MAAA,OAAA,wCAAY,WAAW,KAAK,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAEnE,UAAI,eAAe,eAAe,OAAO,eAAe,MAAM;AAC5D,cAAM,SAAS,eAAe;AAE9B,YAAI,OAAO,YAAY;AACrB,gBAAM,IAAI,MAAM,aAAa,OAAO,SAAS,UAAU,OAAO,UAAU,GAAG;AAAA,QAC5E;AAGDA,sBAAAA,MAAY,MAAA,OAAA,wCAAA,wBAAwB;AACpCA,sBAAY,MAAA,MAAA,OAAA,wCAAA,oBAAoB,OAAO,MAAM;AAC7CA,sBAAA,MAAA,MAAA,OAAA,wCAAY,+BAA8B,YAAO,WAAP,mBAAe,QAAQ;AACjEA,sBAAY,MAAA,MAAA,OAAA,wCAAA,8BAA6B,YAAO,WAAP,mBAAe,OAAO;AAE/D,YAAI,OAAO,UAAU,OAAO,OAAO,aAAa,GAAG;AACjDA,wBAAAA,2DAAY,wBAAwB;AAGpCA,wBAAAA,MAAA,MAAA,OAAA,wCAAY,6BAA6B;AACzCA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,uCAAuC;AACnDA,wBAAY,MAAA,MAAA,OAAA,wCAAA,SAAS,OAAO,OAAO,QAAQ;AAC3CA,wBAAY,MAAA,MAAA,OAAA,wCAAA,SAAS,OAAO,OAAO,OAAO;AAC1CA,wBAAA,MAAA,MAAA,OAAA,wCAAY,SAAS,OAAO,OAAO,WAAW;AAC9CA,wBAAAA,MAAA,MAAA,OAAA,wCAAY,WAAW,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AACtDA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,uCAAuC;AAGnD,gBAAM,cAAc,OAAO,OAAO,eAAe;AACjDA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,4BAA4B,WAAW;AAGnD,gBAAM,WAAW,CAAC;AAAA,YAChB,SAAS,cAAc,CAAC,WAAW,IAAI,CAAE;AAAA,YACzC,KAAK,CAAE;AAAA,YACP,QAAQ,CAAE;AAAA,UACtB,CAAW;AAEDA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,4BAA4B,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAEzE,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,MAAM;AAAA,UACP;AAAA,QACX,WAAmB,OAAO,UAAU,OAAO,OAAO,aAAa,GAAG;AACxDA,mFAAY,+BAA+B,OAAO,OAAO,OAAO,UAAU;AAAA,QACpF,OAAe;AACLA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,6BAA6B,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AACxE,gBAAM,IAAI,MAAM,YAAY;AAAA,QAC7B;AAAA,MAEF;AAAA,IACF;AAEDA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,oBAAoB;AAChC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,CAAE;AAAA,MACR,SAAS;AAAA,IACV;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,wCAAA,kBAAkB,KAAK;AACrC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,CAAE;AAAA,MACR,OAAO,MAAM;AAAA,IACd;AAAA,EACF;AACH;AAOA,MAAM,gBAAgB;AAAA;AAAA,EAEpB;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA;AAAA,EAEpD;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA;AAAA,EAE5D;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA;AAAA,EAEtD;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAM;AAAA;AAAA,EAE/C;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA;AAAA,EAExC;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA;AAAA,EAErC;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAC/C;AAGA,MAAM,YAAY;AAAA,EAChB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAC5D;AAAA,EAAa;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAY;AAAA,EAC9D;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAW;AAChD;AAKA,SAAS,0BAA0B,UAAU;AAC3C,MAAI,CAAC,MAAM,QAAQ,QAAQ;AAAG,WAAO,CAAE;AAEvC,SAAO,SAAS,OAAO,aAAW;AAEhC,QAAI,cAAc,KAAK,cAAY,QAAQ,SAAS,QAAQ,CAAC,GAAG;AAC9D,aAAO;AAAA,IACR;AAGD,QAAI,UAAU,KAAK,SAAO,QAAQ,cAAc,SAAS,GAAG,CAAC,GAAG;AAC9D,aAAO;AAAA,IACR;AAGD,UAAM,kBAAkB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACjE,QAAI,gBAAgB,KAAK,UAAQ,QAAQ,SAAS,IAAI,CAAC,GAAG;AACxD,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACX,CAAG;AACH;AAKA,SAAS,sBAAsB,MAAM;AACnC,MAAI,CAAC,MAAM,QAAQ,IAAI;AAAG,WAAO,CAAE;AAEnC,SAAO,KAAK,OAAO,SAAO;AACxB,UAAM,WAAW,IAAI,YAAa;AAGlC,QAAI,UAAU,KAAK,aAAW,SAAS,SAAS,OAAO,CAAC,GAAG;AACzD,aAAO;AAAA,IACR;AAGD,QAAI,cAAc,KAAK,cAAY,IAAI,SAAS,QAAQ,CAAC,GAAG;AAC1D,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACX,CAAG;AACH;AAKA,SAAS,yBAAyB,SAAS;AACzC,MAAI,CAAC,MAAM,QAAQ,OAAO;AAAG,WAAO,CAAE;AAEtC,SAAO,QAAQ,OAAO,SAAO;AAC3B,UAAM,UAAU,IAAI,QAAQ,IAAI,WAAW;AAG3C,QAAI,cAAc,KAAK,cAAY,QAAQ,SAAS,QAAQ,CAAC,GAAG;AAC9D,aAAO;AAAA,IACR;AAGD,UAAM,aAAa,IAAI,SAAS,IAAI,eAAe;AACnD,QAAI,aAAa,OAAO,UAAU,KAAK,SAAO,QAAQ,YAAa,EAAC,SAAS,GAAG,CAAC,GAAG;AAClF,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACX,CAAG,EAAE,IAAI,UAAQ;AAAA,IACb,MAAM,IAAI,QAAQ,IAAI;AAAA,IACtB,YAAY,IAAI,SAAS,IAAI,eAAe;AAAA,IAC5C,UAAU;AAAA,EACd,EAAI;AACJ;AAKO,eAAe,4BAA4B,UAAU;AAC1D,MAAI;AACFA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,2BAA2B;AACvC,UAAM,cAAc,MAAM,oBAAqB;AAC/C,UAAM,SAAS,MAAM,UAAU,QAAQ;AAEvC,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK,qFAAqF,WAAW;AAAA,MACrG,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,WAAW;AAAA,MACZ;AAAA,MACD,QAAQ;AAAA,QACN,gBAAgB;AAAA,MACjB;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAED,QAAI,SAAS,eAAe,OAAO,SAAS,QAAQ,SAAS,KAAK,QAAQ;AACxEA,oBAAAA,MAAA,MAAA,OAAA,wCAAY,YAAY;AACxBA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,wBAAwB,KAAK,UAAU,SAAS,KAAK,QAAQ,MAAM,CAAC,CAAC;AAGjF,YAAM,sBAAsB,CAAC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,IAAI;AACxF,YAAM,oBAAoB,SAAS,KAAK,OAAO,OAAO,UAAQ;AAC5D,eAAO,oBAAoB,KAAK,aAAW,KAAK,QAAQ,SAAS,OAAO,CAAC,KAAK,KAAK,QAAQ;AAAA,MACnG,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,kBAAkB,IAAI,WAAS;AAAA,UACnC,MAAM,KAAK;AAAA,UACX,YAAY,KAAK;AAAA,UACjB,UAAU;AAAA,QACpB,EAAU;AAAA,MACH;AAAA,IACF;AAED,WAAO,EAAE,SAAS,OAAO,MAAM,CAAA,EAAI;AAAA,EACpC,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,wCAAA,eAAe,KAAK;AAClC,WAAO,EAAE,SAAS,OAAO,MAAM,CAAA,GAAI,OAAO,MAAM,QAAS;AAAA,EAC1D;AACH;AAKO,eAAe,6BAA6B,UAAU;AAC3DA,gBAAAA,MAAA,MAAA,OAAA,wCAAY,iCAAiC;AAC7CA,gBAAAA,MAAA,MAAA,OAAA,wCAAY,yBAAyB,QAAQ;AAE7C,QAAM,iBAAiB;AAAA,IACrB,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC,WAAW;AAAA,IACX,OAAO,CAAE;AAAA,IACT,cAAc,CAAE;AAAA,IAChB,kBAAkB,CAAE;AAAA,IACpB,QAAQ,CAAE;AAAA,IACV,UAAU,CAAE;AAAA,EACb;AAED,MAAI;AAEF,mBAAe,MAAM,KAAK,sBAAsB;AAChDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,wCAAwC;AAEpD,UAAM,cAAc,MAAM,oBAAqB;AAC/C,UAAM,SAAS,MAAM,UAAU,QAAQ;AAEvC,mBAAe,cAAc;AAAA,MAC3B,QAAQ,YAAY;AAAA,MACpB,QAAQ,YAAY,UAAU,GAAG,EAAE,IAAI;AAAA,MACvC,OAAO,YAAY,SAAS;AAAA,IAC7B;AAED,mBAAe,YAAY;AAAA,MACzB,cAAc,OAAO;AAAA,MACrB,SAAS,OAAO,SAAS;AAAA,MACzB,QAAQ,OAAO,UAAU,GAAG,EAAE,IAAI;AAAA,IACnC;AAEDA,kBAAY,MAAA,MAAA,OAAA,wCAAA,kCAAkC,YAAY,MAAM;AAChEA,kBAAA,MAAA,MAAA,OAAA,wCAAY,8BAA8B,OAAO,MAAM;AAGvD,mBAAe,MAAM,KAAK,cAAc;AACxCA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,gCAAgC;AAE5C,UAAM,sBAAsB,MAAM,0BAA0B,QAAQ;AACpE,mBAAe,aAAa,qBAAqB;AAEjDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,8BAA8B;AAC1CA,wBAAA,MAAA,OAAA,wCAAY,KAAK,UAAU,qBAAqB,MAAM,CAAC,CAAC;AAGxD,mBAAe,MAAM,KAAK,gBAAgB;AAC1CA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,kCAAkC;AAE9C,UAAM,gBAAgB,MAAM,4BAA4B,QAAQ;AAChE,mBAAe,aAAa,kBAAkB;AAE9CA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,gCAAgC;AAC5CA,6EAAY,KAAK,UAAU,eAAe,MAAM,CAAC,CAAC;AAGlD,mBAAe,MAAM,KAAK,WAAW;AACrCA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,6BAA6B;AAEzC,QAAI,oBAAoB,WAAW,oBAAoB,MAAM;AAC3D,YAAM,iBAAiB,8BAA8B,oBAAoB,IAAI;AAC7E,qBAAe,iBAAiB,qBAAqB;AAErDA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,6BAA6B;AACzCA,0BAAY,MAAA,OAAA,wCAAA,KAAK,UAAU,gBAAgB,MAAM,CAAC,CAAC;AAAA,IACpD;AAGD,mBAAe,WAAW,2BAA2B,cAAc;AAEnEA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,wBAAwB;AACpCA,6EAAY,IAAI,OAAO,EAAE,CAAC;AAC1BA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU;AACtBA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,KAAK,UAAU,eAAe,UAAU,MAAM,CAAC,CAAC;AAC5DA,6EAAY,IAAI,OAAO,EAAE,CAAC;AAE1B,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wCAAc,0BAA0B,KAAK;AAC7C,mBAAe,OAAO,KAAK;AAAA,MACzB,MAAM;AAAA,MACN,OAAO,MAAM;AAAA,MACb,OAAO,MAAM;AAAA,IACnB,CAAK;AACD,WAAO;AAAA,EACR;AACH;AAMO,eAAe,6BAA6B,UAAU;AAC3DA,gBAAAA,MAAA,MAAA,OAAA,wCAAY,iCAAiC;AAC7CA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,6BAA6B,QAAQ;AACjDA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,sDAAsD;AAElE,MAAI;AAEFA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,+CAA+C;AAC3D,UAAM,eAAe,MAAM,iBAAiB,QAAQ;AAEpD,QAAI,CAAC,aAAa,SAAS;AACzBA,oBAAA,MAAA,MAAA,SAAA,wCAAc,oCAAoC,aAAa,KAAK;AACpE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,aAAa;AAAA,QACpB,MAAM;AAAA,UACJ,SAAS,mBAAmB,aAAa,KAAK;AAAA,UAC9C,YAAY;AAAA,UACZ,iBAAiB,CAAE;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEDA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,iCAAiC;AAC7CA,kBAAY,MAAA,MAAA,OAAA,wCAAA,6BAA6B,aAAa,WAAW;AAGjEA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,4CAA4C;AACxD,UAAM,mBAAmB,MAAM,2BAA2B,aAAa,WAAW;AAElF,QAAI,CAAC,iBAAiB,SAAS;AAC7BA,oBAAA,MAAA,MAAA,SAAA,wCAAc,qCAAqC,iBAAiB,KAAK;AACzE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,iBAAiB;AAAA,QACxB,MAAM;AAAA,UACJ,SAAS,oBAAoB,iBAAiB,KAAK;AAAA,UACnD,YAAY;AAAA,UACZ,iBAAiB,CAAE;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEDA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,kCAAkC;AAC9CA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,4BAA4B,KAAK,UAAU,iBAAiB,MAAM,MAAM,CAAC,CAAC;AAGtFA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,kCAAkC;AAE9C,UAAM,UAAU;AAAA,MACd,iBAAiB,iBAAiB,KAAK,mBAAmB,CAAE;AAAA,MAC5D,SAAS,iBAAiB,KAAK,WAAW;AAAA,MAC1C,YAAY,iBAAiB,KAAK,cAAc;AAAA,MAChD,iBAAiB,aAAa;AAAA,MAC9B,qBAAqB,iBAAiB;AAAA,IACvC;AAEDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,2BAA2B;AACvCA,wBAAA,MAAA,OAAA,wCAAY,aAAa,QAAQ,gBAAgB,MAAM,IAAI;AAC3DA,wBAAY,MAAA,OAAA,wCAAA,YAAY,QAAQ,UAAU,GAAG;AAC7CA,wBAAA,MAAA,OAAA,wCAAY,WAAW,QAAQ,OAAO,EAAE;AACxCA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,4BAA4B;AACxC,YAAQ,gBAAgB,QAAQ,CAAC,MAAM,UAAU;AAC/CA,+EAAY,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,UAAU,KAAK,UAAU,OAAO,KAAK,MAAM,EAAE;AAAA,IAC3F,CAAK;AAED,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,IACP;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,wCAAA,6BAA6B,KAAK;AAChD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,MACb,MAAM;AAAA,QACJ,SAAS,YAAY,MAAM,OAAO;AAAA,QAClC,YAAY;AAAA,QACZ,iBAAiB,CAAE;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACH;AAKA,eAAe,iBAAiB,UAAU;AACxCA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,oCAAoC;AAEhD,MAAI;AAEF,UAAM,SAAS,MAAM,UAAU,QAAQ;AACvCA,kBAAA,MAAA,MAAA,OAAA,wCAAY,iCAAiC,OAAO,MAAM;AAG1D,UAAM,iBAAiB;AAAA,MACrB,SAAS;AAAA,MACT,QAAQ;AAAA;AAAA,MACR,OAAO;AAAA,IACR;AAGD,UAAM,cAAc;AAAA,MAClB,OAAO,eAAe;AAAA,MACtB,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,WAAW;AAAA,gBACT,KAAK,0BAA0B,MAAM;AAAA,cACtC;AAAA,YACF;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD,aAAa;AAAA,MACb,YAAY;AAAA,IACb;AAEDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,yBAAyB;AACrCA,kBAAA,MAAA,MAAA,OAAA,wCAAY,uBAAuB,eAAe,OAAO;AACzDA,kBAAA,MAAA,MAAA,OAAA,wCAAY,sBAAsB,eAAe,KAAK;AAGtD,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK,eAAe;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB,UAAU,eAAe,MAAM;AAAA,QAChD,UAAU;AAAA,MACX;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAEDA,kBAAA,MAAA,MAAA,OAAA,wCAAY,0BAA0B,SAAS,UAAU;AACzDA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,yBAAyB,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC,CAAC;AAE3E,QAAI,SAAS,eAAe,OAAO,SAAS,QAAQ,SAAS,KAAK,WAAW,SAAS,KAAK,QAAQ,CAAC,GAAG;AACrG,YAAM,cAAc,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ;AACrDA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,4BAA4B;AACxCA,oBAAAA,2DAAY,sBAAsB,WAAW;AAE7C,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA,aAAa,SAAS;AAAA,MACvB;AAAA,IACP,OAAW;AACL,YAAM,IAAI,MAAM,uBAAuB,SAAS,UAAU,EAAE;AAAA,IAC7D;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wCAAc,mCAAmC,KAAK;AACtD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM,WAAW;AAAA,IACzB;AAAA,EACF;AACH;AAKA,eAAe,2BAA2B,aAAa;AACrDA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,sCAAsC;AAClDA,gBAAAA,MAAY,MAAA,OAAA,wCAAA,uBAAuB,WAAW;AAE9C,MAAI;AAEF,UAAM,EAAE,mBAAmB,qBAAqB,MAAa;AAE7DA,kBAAAA,MAAA,MAAA,OAAA,wCAAY,2BAA2B;AACvCA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,eAAe,iBAAiB;AAC5CA,wBAAA,MAAA,OAAA,wCAAY,gBAAgB,mBAAmB,iBAAiB,SAAS,CAAC;AAG1E,UAAM,oBAAoB;AAAA;AAAA;AAAA,EAG5B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBTA,kBAAAA,MAAA,MAAA,OAAA,yCAAY,4BAA4B;AACxCA,kBAAY,MAAA,MAAA,OAAA,yCAAA,wBAAwB,kBAAkB,MAAM;AAG5D,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACF;AAAA,MACD,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT;AAGD,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MACjC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB,UAAU,gBAAgB;AAAA,QAC3C,UAAU;AAAA,MACX;AAAA,MACD,SAAS;AAAA,IACf,CAAK;AAEDA,kBAAA,MAAA,MAAA,OAAA,yCAAY,2BAA2B,SAAS,UAAU;AAC1DA,kBAAAA,MAAA,MAAA,OAAA,yCAAY,0BAA0B,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC,CAAC;AAE5E,QAAI,SAAS,eAAe,OAAO,SAAS,QAAQ,SAAS,KAAK,WAAW,SAAS,KAAK,QAAQ,CAAC,GAAG;AACrG,YAAM,oBAAoB,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAC3DA,oBAAAA,4DAAY,8BAA8B;AAC1CA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,wBAAwB,iBAAiB;AAErD,UAAI;AAEF,cAAM,WAAW,KAAK,MAAM,iBAAiB;AAC7CA,sBAAAA,MAAA,MAAA,OAAA,yCAAY,uBAAuB;AACnCA,sBAAAA,MAAA,MAAA,OAAA,yCAAY,yBAAyB,KAAK,UAAU,UAAU,MAAM,CAAC,CAAC;AAEtE,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN,aAAa,SAAS;AAAA,QACvB;AAAA,MACF,SAAQ,YAAY;AACnBA,sBAAAA,8DAAc,0BAA0B,UAAU;AAClDA,sBAAAA,MAAY,MAAA,OAAA,yCAAA,uBAAuB,iBAAiB;AAGpD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,iBAAiB,CAAC;AAAA,cAChB,MAAM;AAAA,cACN,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,UAAU;AAAA,cACV,YAAY,kBAAkB,UAAU,GAAG,GAAG;AAAA,cAC9C,WAAW;AAAA,YACzB,CAAa;AAAA,YACD,SAAS;AAAA,YACT,YAAY;AAAA,UACb;AAAA,UACD,aAAa,SAAS;AAAA,QACvB;AAAA,MACF;AAAA,IACP,OAAW;AACL,YAAM,IAAI,MAAM,qBAAqB,SAAS,UAAU,EAAE;AAAA,IAC3D;AAAA,EAEF,SAAQ,OAAO;AACdA,kBAAAA,8DAAc,iCAAiC,KAAK;AACpD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM,WAAW;AAAA,IACzB;AAAA,EACF;AACH;AAOA,SAAS,8BAA8B,MAAM;AAC3C,QAAM,WAAW;AAAA,IACf,YAAY,KAAK;AAAA,IACjB,iBAAiB,CAAE;AAAA,IACnB,aAAa,CAAE;AAAA,IACf,gBAAgB,CAAE;AAAA,IAClB,kBAAkB,CAAE;AAAA,IACpB,cAAc,CAAE;AAAA,IAChB,wBAAwB,CAAE;AAAA,IAC1B,iBAAiB,CAAE;AAAA,EACpB;AAED,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5BA,kBAAA,MAAA,MAAA,OAAA,yCAAY,sBAAsB,QAAQ,CAAC,KAAK,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAG7E,QAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,eAAS,gBAAgB,QAAQ,KAAK,EAAE,IAAI;AAAA,QAC1C,OAAO,KAAK,QAAQ;AAAA,QACpB,UAAU,KAAK;AAAA,QACf,aAAa,0BAA0B,KAAK,OAAO;AAAA,QACnD,gBAAgB,KAAK,QAAQ,OAAO,QAAM,CAAC,0BAA0B,CAAC,EAAE,CAAC,EAAE,MAAM;AAAA,MAClF;AAAA,IACF;AAGD,QAAI,KAAK,OAAO,MAAM,QAAQ,KAAK,GAAG,GAAG;AACvC,eAAS,YAAY,QAAQ,KAAK,EAAE,IAAI;AAAA,QACtC,OAAO,KAAK,IAAI;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,aAAa,sBAAsB,KAAK,GAAG;AAAA,QAC3C,gBAAgB,KAAK,IAAI,OAAO,SAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE,MAAM;AAAA,MAC5E;AAAA,IACF;AAGD,QAAI,KAAK,UAAU,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC7C,eAAS,eAAe,QAAQ,KAAK,EAAE,IAAI;AAAA,QACzC,OAAO,KAAK,OAAO;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,aAAa,yBAAyB,KAAK,MAAM;AAAA,QACjD,gBAAgB,KAAK,OAAO,OAAO,SAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE,MAAM;AAAA,MAClF;AAAA,IACF;AAGD,UAAM,cAAc,KAAK,WAAW,0BAA0B,KAAK,OAAO,EAAE,SAAS;AACrF,UAAM,UAAU,KAAK,OAAO,sBAAsB,KAAK,GAAG,EAAE,SAAS;AACrE,UAAM,aAAa,KAAK,UAAU,yBAAyB,KAAK,MAAM,EAAE,SAAS;AAEjF,QAAI,eAAe,WAAW,YAAY;AACxC,eAAS,iBAAiB,KAAK;AAAA,QAC7B;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACT,CAAO;AAAA,IACP,OAAW;AACL,eAAS,aAAa,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACjB,CAAO;AAAA,IACF;AAAA,EACL,CAAG;AAGD,MAAI,SAAS,iBAAiB,WAAW,GAAG;AAC1C,aAAS,gBAAgB,KAAK,cAAc;AAAA,EAC7C;AAED,MAAI,SAAS,aAAa,SAAS,SAAS,iBAAiB,QAAQ;AACnE,aAAS,gBAAgB,KAAK,aAAa;AAAA,EAC5C;AAGD,QAAM,cAAc,OAAO,OAAO,SAAS,eAAe,EAAE,QAAQ,QAAM,GAAG,QAAQ;AACrF,QAAM,qBAAqB,CAAC,MAAM,SAAS,MAAM,OAAO;AACxD,QAAM,wBAAwB,YAAY;AAAA,IAAK,QAC7C,mBAAmB,KAAK,gBAAc,GAAG,SAAS,UAAU,CAAC;AAAA,EAC9D;AAED,MAAI,uBAAuB;AACzB,aAAS,gBAAgB,KAAK,qBAAqB;AAAA,EACpD;AAED,SAAO;AACT;AAKA,SAAS,2BAA2B,gBAAgB;;AAClD,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,QAAQ,CAAE;AAAA,IACV,iBAAiB,CAAE;AAAA,IACnB,WAAW,CAAE;AAAA,IACb,aAAa,CAAE;AAAA,IACf,iBAAiB,CAAE;AAAA,EACpB;AAGD,WAAS,YAAY;AAAA,IACnB,oBAAoB;AAAA,MAClB,QAAQ,CAAC,CAAC,eAAe,aAAa;AAAA,MACtC,WAAS,oBAAe,aAAa,uBAA5B,mBAAgD,YAAW;AAAA,MACpE,SAAS,CAAC,GAAE,0BAAe,aAAa,uBAA5B,mBAAgD,SAAhD,mBAAsD;AAAA,MAClE,aAAW,0BAAe,aAAa,uBAA5B,mBAAgD,SAAhD,mBAAsD,WAAU;AAAA,IAC5E;AAAA,IACD,iBAAiB;AAAA,MACf,QAAQ,CAAC,CAAC,eAAe,aAAa;AAAA,MACtC,WAAS,oBAAe,aAAa,oBAA5B,mBAA6C,YAAW;AAAA,MACjE,SAAS,CAAC,GAAE,0BAAe,aAAa,oBAA5B,mBAA6C,SAA7C,mBAAmD;AAAA,MAC/D,aAAW,0BAAe,aAAa,oBAA5B,mBAA6C,SAA7C,mBAAmD,WAAU;AAAA,IACzE;AAAA,EACF;AAGD,MAAI,eAAe,iBAAiB,oBAAoB;AACtD,UAAM,YAAY,eAAe,iBAAiB;AAClD,aAAS,cAAc;AAAA,MACrB,YAAY,UAAU;AAAA,MACtB,kBAAkB,UAAU,iBAAiB;AAAA,MAC7C,cAAc,UAAU,aAAa;AAAA,MACrC,WAAW,UAAU,aAAa,KAC/B,UAAU,iBAAiB,SAAS,UAAU,aAAa,KAAK,QAAQ,CAAC,IAAI,MAAM;AAAA,MACtF,iBAAiB,UAAU;AAAA,IAC5B;AAAA,EACF;AAGD,MAAI,CAAC,SAAS,UAAU,mBAAmB,SAAS;AAClD,aAAS,OAAO,KAAK,aAAa;AAClC,aAAS,gBAAgB,KAAK,cAAc;AAAA,EAC7C;AAED,MAAI,SAAS,UAAU,mBAAmB,WAAW,CAAC,SAAS,UAAU,mBAAmB,SAAS;AACnG,aAAS,OAAO,KAAK,iBAAiB;AACtC,aAAS,gBAAgB,KAAK,gBAAgB;AAAA,EAC/C;AAED,MAAI,SAAS,YAAY,qBAAqB,GAAG;AAC/C,aAAS,OAAO,KAAK,cAAc;AACnC,aAAS,gBAAgB,KAAK,eAAe;AAAA,EAC9C;AAED,OAAI,cAAS,YAAY,oBAArB,mBAAsC,SAAS,wBAAwB;AACzE,aAAS,OAAO,KAAK,mBAAmB;AACxC,aAAS,gBAAgB,KAAK,8BAA8B;AAC5D,aAAS,gBAAgB,KAAK,aAAa;AAAA,EAC5C;AAGD,QAAM,aAAa,SAAS,OAAO;AACnC,QAAM,sBAAsB,SAAS,gBAAgB;AAErD,WAAS,UAAU,WAAW,UAAU,WAAW,mBAAmB;AAEtE,MAAI,eAAe,GAAG;AACpB,aAAS,WAAW;AAAA,EACxB,OAAS;AACL,aAAS,WAAW,SAAS,SAAS,OAAO,CAAC,CAAC;AAAA,EAChD;AAED,SAAO;AACT;;;;;;;"}
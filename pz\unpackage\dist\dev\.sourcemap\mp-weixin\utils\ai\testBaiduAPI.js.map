{"version": 3, "file": "testBaiduAPI.js", "sources": ["utils/ai/testBaiduAPI.js"], "sourcesContent": ["// 百度AI API测试脚本\n// 用于验证API配置和连接状态\n\nimport { getBaiduAccessToken, BAIDU_CONFIG } from './baiduImageService.js'\n\n/**\n * 测试百度API连接和配置\n */\nexport async function testBaiduAPIConnection() {\n  console.log('🧪 开始测试百度AI API连接...')\n  \n  const testResults = {\n    tokenTest: false,\n    apiEndpoints: {},\n    configuration: {},\n    recommendations: []\n  }\n\n  try {\n    // 1. 测试配置信息\n    console.log('📋 检查API配置...')\n    testResults.configuration = {\n      hasApiKey: !!BAIDU_CONFIG.apiKey,\n      hasSecretKey: !!BAIDU_CONFIG.secretKey,\n      apiKeyLength: BAIDU_CONFIG.apiKey ? BAIDU_CONFIG.apiKey.length : 0,\n      secretKeyLength: BAIDU_CONFIG.secretKey ? BAIDU_CONFIG.secretKey.length : 0,\n      endpoints: {\n        tokenUrl: BAIDU_CONFIG.tokenUrl,\n        dishDetectUrl: BAIDU_CONFIG.dishDetectUrl,\n        ingredientUrl: BAIDU_CONFIG.ingredientUrl,\n        imageUnderstandingRequestUrl: BAIDU_CONFIG.imageUnderstandingRequestUrl\n      }\n    }\n\n    if (!BAIDU_CONFIG.apiKey || !BAIDU_CONFIG.secretKey) {\n      testResults.recommendations.push('❌ API密钥或密钥为空，请检查配置')\n      return testResults\n    }\n\n    // 2. 测试Access Token获取\n    console.log('🔑 测试Access Token获取...')\n    try {\n      const accessToken = await getBaiduAccessToken()\n      if (accessToken && accessToken.length > 0) {\n        testResults.tokenTest = true\n        console.log('✅ Access Token获取成功')\n        console.log('🔍 Token长度:', accessToken.length)\n        console.log('🔍 Token前10位:', accessToken.substring(0, 10) + '...')\n      } else {\n        testResults.recommendations.push('❌ Access Token获取失败或为空')\n      }\n    } catch (tokenError) {\n      console.error('❌ Access Token获取失败:', tokenError)\n      testResults.recommendations.push(`❌ Token获取错误: ${tokenError.message}`)\n    }\n\n    // 3. 测试API端点可达性\n    console.log('🌐 测试API端点可达性...')\n    const endpoints = [\n      { name: '菜品识别', url: BAIDU_CONFIG.dishDetectUrl },\n      { name: '果蔬识别', url: BAIDU_CONFIG.ingredientUrl },\n      { name: '图像理解请求', url: BAIDU_CONFIG.imageUnderstandingRequestUrl },\n      { name: '图像理解结果', url: BAIDU_CONFIG.imageUnderstandingResultUrl }\n    ]\n\n    for (const endpoint of endpoints) {\n      try {\n        // 简单的连接测试（不发送实际请求）\n        const testUrl = new URL(endpoint.url)\n        testResults.apiEndpoints[endpoint.name] = {\n          url: endpoint.url,\n          host: testUrl.host,\n          protocol: testUrl.protocol,\n          accessible: true // 假设可达，实际项目中可以做更详细的测试\n        }\n        console.log(`✅ ${endpoint.name} 端点配置正确`)\n      } catch (urlError) {\n        testResults.apiEndpoints[endpoint.name] = {\n          url: endpoint.url,\n          accessible: false,\n          error: urlError.message\n        }\n        testResults.recommendations.push(`❌ ${endpoint.name} 端点URL格式错误`)\n      }\n    }\n\n    // 4. 生成优化建议\n    if (testResults.tokenTest) {\n      testResults.recommendations.push('✅ 基础连接测试通过')\n      testResults.recommendations.push('💡 建议：使用真实图片进行完整功能测试')\n    }\n\n    console.log('🧪 百度AI API连接测试完成')\n    return testResults\n\n  } catch (error) {\n    console.error('❌ API连接测试失败:', error)\n    testResults.recommendations.push(`❌ 测试过程出错: ${error.message}`)\n    return testResults\n  }\n}\n\n/**\n * 生成测试报告\n */\nexport function generateTestReport(testResults) {\n  console.log('\\n📊 百度AI API测试报告')\n  console.log('=' .repeat(50))\n  \n  console.log('\\n🔧 配置检查:')\n  console.log(`  API Key: ${testResults.configuration.hasApiKey ? '✅ 已配置' : '❌ 未配置'}`)\n  console.log(`  Secret Key: ${testResults.configuration.hasSecretKey ? '✅ 已配置' : '❌ 未配置'}`)\n  \n  console.log('\\n🔑 Token测试:')\n  console.log(`  获取状态: ${testResults.tokenTest ? '✅ 成功' : '❌ 失败'}`)\n  \n  console.log('\\n🌐 API端点:')\n  Object.entries(testResults.apiEndpoints).forEach(([name, info]) => {\n    console.log(`  ${name}: ${info.accessible ? '✅ 正常' : '❌ 异常'}`)\n  })\n  \n  console.log('\\n💡 建议:')\n  testResults.recommendations.forEach(rec => {\n    console.log(`  ${rec}`)\n  })\n  \n  console.log('\\n' + '=' .repeat(50))\n}\n\n/**\n * 快速诊断函数\n */\nexport async function quickDiagnosis() {\n  console.log('🚀 开始快速诊断百度AI配置...')\n  \n  const testResults = await testBaiduAPIConnection()\n  generateTestReport(testResults)\n  \n  // 返回诊断结果\n  return {\n    success: testResults.tokenTest,\n    issues: testResults.recommendations.filter(rec => rec.includes('❌')),\n    suggestions: testResults.recommendations.filter(rec => rec.includes('💡'))\n  }\n}\n"], "names": ["uni", "BAIDU_CONFIG", "getBaiduAccessToken"], "mappings": ";;;AAQO,eAAe,yBAAyB;AAC7CA,gBAAAA,MAAY,MAAA,OAAA,kCAAA,sBAAsB;AAElC,QAAM,cAAc;AAAA,IAClB,WAAW;AAAA,IACX,cAAc,CAAE;AAAA,IAChB,eAAe,CAAE;AAAA,IACjB,iBAAiB,CAAE;AAAA,EACpB;AAED,MAAI;AAEFA,kBAAAA,qDAAY,eAAe;AAC3B,gBAAY,gBAAgB;AAAA,MAC1B,WAAW,CAAC,CAACC,2BAAAA,aAAa;AAAA,MAC1B,cAAc,CAAC,CAACA,2BAAAA,aAAa;AAAA,MAC7B,cAAcA,2BAAY,aAAC,SAASA,2BAAY,aAAC,OAAO,SAAS;AAAA,MACjE,iBAAiBA,2BAAY,aAAC,YAAYA,2BAAY,aAAC,UAAU,SAAS;AAAA,MAC1E,WAAW;AAAA,QACT,UAAUA,2BAAY,aAAC;AAAA,QACvB,eAAeA,2BAAY,aAAC;AAAA,QAC5B,eAAeA,2BAAY,aAAC;AAAA,QAC5B,8BAA8BA,2BAAY,aAAC;AAAA,MAC5C;AAAA,IACF;AAED,QAAI,CAACA,2BAAAA,aAAa,UAAU,CAACA,2BAAY,aAAC;AAAW;AAMrDD,kBAAAA,MAAA,MAAA,OAAA,kCAAY,wBAAwB;AACpC,QAAI;AACF,YAAM,cAAc,MAAME,+CAAqB;AAC/C,UAAI,eAAe,YAAY,SAAS,GAAG;AACzC,oBAAY,YAAY;AACxBF,sBAAAA,MAAA,MAAA,OAAA,kCAAY,oBAAoB;AAChCA,sBAAY,MAAA,MAAA,OAAA,kCAAA,eAAe,YAAY,MAAM;AAC7CA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,iBAAiB,YAAY,UAAU,GAAG,EAAE,IAAI,KAAK;AAAA,MACzE,OAAa;AACL,oBAAY,gBAAgB,KAAK,uBAAuB;AAAA,MACzD;AAAA,IACF,SAAQ,YAAY;AACnBA,oBAAAA,uDAAc,uBAAuB,UAAU;AAC/C,kBAAY,gBAAgB,KAAK,gBAAgB,WAAW,OAAO,EAAE;AAAA,IACtE;AAGDA,kBAAAA,MAAA,MAAA,OAAA,kCAAY,kBAAkB;AAC9B,UAAM,YAAY;AAAA,MAChB,EAAE,MAAM,QAAQ,KAAKC,2BAAAA,aAAa,cAAe;AAAA,MACjD,EAAE,MAAM,QAAQ,KAAKA,2BAAAA,aAAa,cAAe;AAAA,MACjD,EAAE,MAAM,UAAU,KAAKA,2BAAAA,aAAa,6BAA8B;AAAA,MAClE,EAAE,MAAM,UAAU,KAAKA,2BAAAA,aAAa,4BAA6B;AAAA,IAClE;AAED,eAAW,YAAY,WAAW;AAChC,UAAI;AAEF,cAAM,UAAU,IAAI,IAAI,SAAS,GAAG;AACpC,oBAAY,aAAa,SAAS,IAAI,IAAI;AAAA,UACxC,KAAK,SAAS;AAAA,UACd,MAAM,QAAQ;AAAA,UACd,UAAU,QAAQ;AAAA,UAClB,YAAY;AAAA;AAAA,QACb;AACDD,4BAAY,MAAA,OAAA,kCAAA,KAAK,SAAS,IAAI,SAAS;AAAA,MACxC,SAAQ,UAAU;AACjB,oBAAY,aAAa,SAAS,IAAI,IAAI;AAAA,UACxC,KAAK,SAAS;AAAA,UACd,YAAY;AAAA,UACZ,OAAO,SAAS;AAAA,QACjB;AACD,oBAAY,gBAAgB,KAAK,KAAK,SAAS,IAAI,YAAY;AAAA,MAChE;AAAA,IACF;AAGD,QAAI,YAAY,WAAW;AACzB,kBAAY,gBAAgB,KAAK,YAAY;AAC7C,kBAAY,gBAAgB,KAAK,sBAAsB;AAAA,IACxD;AAEDA,kBAAAA,MAAA,MAAA,OAAA,kCAAY,mBAAmB;AAC/B,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB,KAAK;AACnC,gBAAY,gBAAgB,KAAK,aAAa,MAAM,OAAO,EAAE;AAC7D,WAAO;AAAA,EACR;AACH;AAKO,SAAS,mBAAmB,aAAa;AAC9CA,gBAAAA,sDAAY,mBAAmB;AAC/BA,gBAAY,MAAA,MAAA,OAAA,mCAAA,IAAK,OAAO,EAAE,CAAC;AAE3BA,gBAAAA,MAAA,MAAA,OAAA,mCAAY,YAAY;AACxBA,gBAAAA,MAAA,MAAA,OAAA,mCAAY,cAAc,YAAY,cAAc,YAAY,UAAU,OAAO,EAAE;AACnFA,gBAAAA,MAAY,MAAA,OAAA,mCAAA,iBAAiB,YAAY,cAAc,eAAe,UAAU,OAAO,EAAE;AAEzFA,gBAAAA,MAAY,MAAA,OAAA,mCAAA,eAAe;AAC3BA,gBAAAA,MAAY,MAAA,OAAA,mCAAA,WAAW,YAAY,YAAY,SAAS,MAAM,EAAE;AAEhEA,gBAAAA,MAAA,MAAA,OAAA,mCAAY,aAAa;AACzB,SAAO,QAAQ,YAAY,YAAY,EAAE,QAAQ,CAAC,CAAC,MAAM,IAAI,MAAM;AACjEA,kBAAAA,MAAY,MAAA,OAAA,mCAAA,KAAK,IAAI,KAAK,KAAK,aAAa,SAAS,MAAM,EAAE;AAAA,EACjE,CAAG;AAEDA,gBAAAA,MAAA,MAAA,OAAA,mCAAY,UAAU;AACtB,cAAY,gBAAgB,QAAQ,SAAO;AACzCA,kBAAA,MAAA,MAAA,OAAA,mCAAY,KAAK,GAAG,EAAE;AAAA,EAC1B,CAAG;AAEDA,sBAAY,MAAA,OAAA,mCAAA,OAAO,IAAK,OAAO,EAAE,CAAC;AACpC;AAKO,eAAe,iBAAiB;AACrCA,gBAAAA,sDAAY,oBAAoB;AAEhC,QAAM,cAAc,MAAM,uBAAwB;AAClD,qBAAmB,WAAW;AAG9B,SAAO;AAAA,IACL,SAAS,YAAY;AAAA,IACrB,QAAQ,YAAY,gBAAgB,OAAO,SAAO,IAAI,SAAS,GAAG,CAAC;AAAA,IACnE,aAAa,YAAY,gBAAgB,OAAO,SAAO,IAAI,SAAS,IAAI,CAAC;AAAA,EAC1E;AACH;;"}